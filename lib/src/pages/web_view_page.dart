import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:image_picker/image_picker.dart';
import 'package:provider/provider.dart';
import 'package:up_school_parent/src/core/utils/app_constants.dart';
import 'package:up_school_parent/src/pages/services/download_methods.dart';
import 'package:up_school_parent/src/pages/widgets/no_internet_connection_widget.dart';
import 'package:xr_helper/xr_helper.dart';

import '../core/config/app_config.dart';

class WebViewPage extends StatefulWidget {
  const WebViewPage({super.key});

  @override
  State<WebViewPage> createState() => _WebViewPageState();
}

class _WebViewPageState extends State<WebViewPage> {
  InAppWebViewController? webViewController;
  final ImagePicker _picker = ImagePicker();

  @override
  void initState() {
    super.initState();
    // Force reload when the widget is initialized
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _forceReload();
    });
  }

  // Force reload the WebView to get latest content
  Future<void> _forceReload() async {
    if (webViewController != null) {
      await webViewController!.reload();
    }
  }

  // Handle file upload for WebView
  Future<List<String>> _handleFileUpload() async {
    try {
      // Show options for camera or gallery
      final result = await showModalBottomSheet<ImageSource>(
        context: context,
        builder: (BuildContext context) {
          return SafeArea(
            child: Wrap(
              children: <Widget>[
                ListTile(
                  leading: const Icon(Icons.photo_library),
                  title: const Text('Gallery'),
                  onTap: () => Navigator.of(context).pop(ImageSource.gallery),
                ),
                ListTile(
                  leading: const Icon(Icons.photo_camera),
                  title: const Text('Camera'),
                  onTap: () => Navigator.of(context).pop(ImageSource.camera),
                ),
                ListTile(
                  leading: const Icon(Icons.cancel),
                  title: const Text('Cancel'),
                  onTap: () => Navigator.of(context).pop(),
                ),
              ],
            ),
          );
        },
      );

      if (result != null) {
        final XFile? pickedFile = await _picker.pickImage(
          source: result,
          maxWidth: 1920,
          maxHeight: 1080,
          imageQuality: 85,
        );

        if (pickedFile != null) {
          Log.i('File picked: ${pickedFile.path}');
          return [pickedFile.path];
        }
      }
    } catch (e) {
      Log.e('Error picking file: $e');
    }
    return [];
  }

  @override
  Widget build(BuildContext context) {
    void onDownloadStart(controller, request) async {
      Log.i("onDownloadStartSSSS $request");

      final data = await controller.evaluateJavascript(
          source: "window.localStorage.getItem('UPSTlink')");

      Log.i("onDownloadStartSSSS444 $data");

      try {
        await prepareSaveDir();
        {
          final lang = await controller.evaluateJavascript(
              source: "window.localStorage.getItem('UPSTlang')");

          final isArabic = lang.toString().contains("ar");

          context
              .showBarMessage(isArabic ? "...جار ي الفتح" : "פותח את הקובץ...");

          downloadFiles(context, controller: controller);
        }
      } catch (e) {
        Log.e('DownloadError: $e');
      }
    }

    return Scaffold(body: SafeArea(
      child: Consumer<AppConfig>(
        builder: (context, appConfig, child) {
          if (!appConfig.hasInternet) {
            return const NoInternetConnectionWidget();
          }

          return InAppWebView(
            initialSettings: InAppWebViewSettings(
              cacheEnabled: false,
              clearCache: true,
              domStorageEnabled:
                  true, // Ensure localStorage/sessionStorage work
              allowFileAccessFromFileURLs: true,
              allowUniversalAccessFromFileURLs: true,
              mediaPlaybackRequiresUserGesture: false,
            ),

            // Add JavaScript handlers for file upload
            onWebViewCreated: (controller) async {
              webViewController = controller;
              appConfig.onWebViewCreated(controller);

              // Configure cache settings to disable caching but preserve sessions
              await controller.setSettings(
                  settings: InAppWebViewSettings(
                cacheEnabled: false,
                clearCache: true,
                domStorageEnabled: true,
                allowFileAccessFromFileURLs: true,
                allowUniversalAccessFromFileURLs: true,
                mediaPlaybackRequiresUserGesture: false,
              ));

              // Add JavaScript handler for file upload
              controller.addJavaScriptHandler(
                handlerName: 'fileUploadHandler',
                callback: (args) async {
                  Log.i('File upload handler called');
                  try {
                    final filePaths = await _handleFileUpload();
                    if (filePaths.isNotEmpty) {
                      return {'success': true, 'filePath': filePaths.first};
                    }
                    return {'success': false, 'error': 'No file selected'};
                  } catch (e) {
                    Log.e('Error in file upload handler: $e');
                    return {'success': false, 'error': e.toString()};
                  }
                },
              );

              // Force reload to get latest content
              await _forceReload();
            },
            onLoadStop: (controller, url) async {
              await appConfig.addTokenToLogin(controller: controller);

              // Inject JavaScript to log file input interactions for debugging
              await controller.evaluateJavascript(source: '''
                console.log('WebView loaded - File upload debugging enabled');

                // Log when file inputs are clicked
                document.addEventListener('click', function(e) {
                  if (e.target.type === 'file') {
                    console.log('File input clicked:', e.target);
                  }
                }, true);

                // Log when file inputs change
                document.addEventListener('change', function(e) {
                  if (e.target.type === 'file') {
                    console.log('File input changed:', e.target.files);
                  }
                }, true);
              ''');
            },
            onProgressChanged: (controller, progress) async {
              if (progress == 100) {
                await appConfig.addTokenToLogin(controller: controller);
              }
            },
            onUpdateVisitedHistory: (controller, url, androidIsReload) async {
              await appConfig.addTokenToLogin(controller: controller);
            },
            onDownloadStartRequest: onDownloadStart,
            onReceivedServerTrustAuthRequest: (controller, challenge) async {
              return ServerTrustAuthResponse(
                  action: ServerTrustAuthResponseAction.PROCEED);
            },
            initialUrlRequest: URLRequest(
              url: WebUri.uri(
                Uri.parse(
                    "${AppConstants.appUrl}?v=${DateTime.now().millisecondsSinceEpoch}"),
              ),
            ),
          );
        },
      ),
    ));
  }
}
