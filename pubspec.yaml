name: up_school_parent
description: UpSchool School App.

publish_to: 'none'

version: 2.1.132+232

environment:
  sdk: '>=3.0.6 <4.0.0'

dependencies:
  flutter:
    sdk: flutter

  # * Helper Package *
  xr_helper:
    path: packages/xr_helper

  cupertino_icons: ^1.0.6

  #! Webview
  flutter_inappwebview: ^6.1.5
  webview_flutter: ^4.7.0
#  flutter_downloader: ^1.11.7
#  flutter_downloader: ^1.11.6
  android_path_provider: ^0.3.1
#  open_file:
  share_plus:
  permission_handler: ^11.3.1
  image_picker: ^1.0.4

  #! Firebase
  #  firebase_core: ^2.24.2
  #  firebase_messaging: ^14.7.10

  #! Restart App
  restart_app: ^1.2.1

  #! State Management
  provider: ^6.1.1
  riverpod:

  #! Animated
  lottie: ^3.0.0

  #! Check Connectivity
  internet_connection_checker: ^1.0.0+1

  #! Notifications
  onesignal_flutter: ^5.3.4
  #  onesignal_flutter: ^5.0.4
#  flutter_downloader: ^1.11.6

  http:
  path_provider:
#  lecle_downloads_path_provider: ^0.0.2+8
  device_info_plus: ^10.1.0

  dio: ^5.7.0
  open_filex: ^4.7.0
  url_launcher:
#  ext_storage:

dev_dependencies:
  flutter_test:
    sdk: flutter

  flutter_lints: ^5.0.0
  flutter_launcher_icons:

dependency_overrides:
  archive: ^3.6.1
  win32: ^5.5.4


flutter:

  uses-material-design: true

#? dart run flutter_launcher_icons:main
flutter_launcher_icons:
  android: true
  ios: true
  image_path: "assets/images/logo.png"
  remove_alpha_ios: true


  assets:
    - assets/images/
    - assets/animated/
    - shorebird.yaml