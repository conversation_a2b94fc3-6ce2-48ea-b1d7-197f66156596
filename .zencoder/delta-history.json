{"snapshots": {"/Users/<USER>/Flutter-Projects/Ajory/New School/teacher-mobile-app/lib/src/pages/web_view_page.dart": {"filePath": "/Users/<USER>/Flutter-Projects/Ajory/New School/teacher-mobile-app/lib/src/pages/web_view_page.dart", "baseContent": "import 'package:flutter/material.dart';\nimport 'package:flutter_inappwebview/flutter_inappwebview.dart';\nimport 'package:provider/provider.dart';\nimport 'package:up_school_parent/src/core/utils/app_constants.dart';\nimport 'package:up_school_parent/src/pages/services/download_methods.dart';\nimport 'package:up_school_parent/src/pages/widgets/no_internet_connection_widget.dart';\nimport 'package:xr_helper/xr_helper.dart';\n\nimport '../core/config/app_config.dart';\n\nclass WebViewPage extends StatefulWidget {\n  const WebViewPage({super.key});\n\n  @override\n  State<WebViewPage> createState() => _WebViewPageState();\n}\n\nclass _WebViewPageState extends State<WebViewPage> {\n  InAppWebViewController? webViewController;\n\n  @override\n  void initState() {\n    super.initState();\n    // Force reload when the widget is initialized\n    WidgetsBinding.instance.addPostFrameCallback((_) {\n      _forceReload();\n    });\n  }\n\n  // Force reload the WebView to get latest content\n  Future<void> _forceReload() async {\n    if (webViewController != null) {\n      await webViewController!.reload();\n    }\n  }\n  \n  @override\n  Widget build(BuildContext context) {\n    void onDownloadStart(controller, request) async {\n      Log.i(\"onDownloadStartSSSS $request\");\n\n      final data = await controller.evaluateJavascript(\n          source: \"window.localStorage.getItem('UPSTlink')\");\n\n      Log.i(\"onDownloadStartSSSS444 $data\");\n\n      try {\n        await prepareSaveDir();\n        {\n          final lang = await controller.evaluateJavascript(\n              source: \"window.localStorage.getItem('UPSTlang')\");\n\n          final isArabic = lang.toString().contains(\"ar\");\n\n          context.showBarMessage(\n              isArabic ? \"...جار ي الفتح\" : \"פותח את הקובץ...\");\n\n          downloadFiles(context,controller: controller);\n        }\n      } catch (e) {\n        Log.e('DownloadError: $e');\n      }\n    }\n\n    return Scaffold(body: SafeArea(\n      child: Consumer<AppConfig>(\n        builder: (context, appConfig, child) {\n          if (!appConfig.hasInternet) {\n            return const NoInternetConnectionWidget();\n          }\n\n          return InAppWebView(\n            onWebViewCreated: appConfig.onWebViewCreated,\n            onLoadStop: (controller, url) async {\n              await appConfig.addTokenToLogin(controller: controller);\n            },\n            onProgressChanged: (controller, progress) async {\n              if (progress == 100) {\n                await appConfig.addTokenToLogin(controller: controller);\n              }\n            },\n            onUpdateVisitedHistory: (controller, url, androidIsReload) async {\n              await appConfig.addTokenToLogin(controller: controller);\n            },\n            onDownloadStartRequest: onDownloadStart,\n            onReceivedServerTrustAuthRequest: (controller, challenge) async {\n              return ServerTrustAuthResponse(\n                  action: ServerTrustAuthResponseAction.PROCEED);\n            },\n            initialUrlRequest: URLRequest(\n              url: WebUri.uri(\n                Uri.parse(\n                  \"${AppConstants.appUrl}?v=${DateTime.now().millisecondsSinceEpoch}\"\n                ),\n              ),\n            ),\n          );\n        },\n      ),\n    ));\n  }\n}\n", "baseTimestamp": 1758013005658, "deltas": [{"timestamp": 1758013018873, "changes": [{"type": "MODIFY", "lineNumber": 72, "content": "            initialSettings: InAppWebViewSettings(", "oldContent": "            onWebViewCreated: appConfig.onWebViewCreated,"}, {"type": "INSERT", "lineNumber": 73, "content": "              cacheEnabled: false,"}, {"type": "INSERT", "lineNumber": 74, "content": "              clearCache: true,"}, {"type": "INSERT", "lineNumber": 75, "content": "              domStorageEnabled:"}, {"type": "INSERT", "lineNumber": 76, "content": "              true, // Ensure localStorage/sessionStorage work"}, {"type": "INSERT", "lineNumber": 77, "content": "            ),"}, {"type": "INSERT", "lineNumber": 78, "content": "            onWebViewCreated: (controller) async {"}, {"type": "INSERT", "lineNumber": 79, "content": "              webViewController = controller;"}, {"type": "INSERT", "lineNumber": 80, "content": "              await appConfig.onWebViewCreated(controller);"}, {"type": "INSERT", "lineNumber": 81, "content": ""}, {"type": "INSERT", "lineNumber": 82, "content": "              // Configure cache settings to disable caching but preserve sessions"}, {"type": "INSERT", "lineNumber": 83, "content": "              await controller.setSettings("}, {"type": "INSERT", "lineNumber": 84, "content": "                  settings: InAppWebViewSettings("}, {"type": "INSERT", "lineNumber": 85, "content": "                    cacheEnabled: false,"}, {"type": "INSERT", "lineNumber": 86, "content": "                    clearCache: true,"}, {"type": "INSERT", "lineNumber": 87, "content": "                    domStorageEnabled: true,"}, {"type": "INSERT", "lineNumber": 88, "content": "                  ));"}, {"type": "INSERT", "lineNumber": 89, "content": ""}, {"type": "INSERT", "lineNumber": 90, "content": "              // Force reload to get latest content"}, {"type": "INSERT", "lineNumber": 91, "content": "              await _forceReload();"}, {"type": "INSERT", "lineNumber": 92, "content": "            },"}]}, {"timestamp": 1758013061354, "changes": [{"type": "DELETE", "lineNumber": 74, "oldContent": "            onLoadStop: (controller, url) async {"}, {"type": "DELETE", "lineNumber": 76, "oldContent": "              await appConfig.addTokenToLogin(controller: controller);"}, {"type": "DELETE", "lineNumber": 78, "oldContent": "            },"}, {"type": "DELETE", "lineNumber": 80, "oldContent": "            onProgressChanged: (controller, progress) async {"}, {"type": "DELETE", "lineNumber": 82, "oldContent": "              if (progress == 100) {"}, {"type": "DELETE", "lineNumber": 84, "oldContent": "                await appConfig.addTokenToLogin(controller: controller);"}, {"type": "DELETE", "lineNumber": 86, "oldContent": "              }"}, {"type": "DELETE", "lineNumber": 88, "oldContent": "            },"}, {"type": "DELETE", "lineNumber": 90, "oldContent": "            onUpdateVisitedHistory: (controller, url, androidIsReload) async {"}, {"type": "DELETE", "lineNumber": 92, "oldContent": "              await appConfig.addTokenToLogin(controller: controller);"}, {"type": "DELETE", "lineNumber": 94, "oldContent": "            },"}, {"type": "DELETE", "lineNumber": 96, "oldContent": "            onDownloadStartRequest: onDownloadStart,"}, {"type": "DELETE", "lineNumber": 98, "oldContent": "            onReceivedServerTrustAuthRequest: (controller, challenge) async {"}, {"type": "DELETE", "lineNumber": 100, "oldContent": "              return ServerTrustAuthResponse("}, {"type": "DELETE", "lineNumber": 102, "oldContent": "                  action: ServerTrustAuthResponseAction.PROCEED);"}, {"type": "DELETE", "lineNumber": 104, "oldContent": "            },"}, {"type": "DELETE", "lineNumber": 106, "oldContent": "            initialUrlRequest: URLRequest("}, {"type": "DELETE", "lineNumber": 108, "oldContent": "              url: WebUri.uri("}, {"type": "DELETE", "lineNumber": 110, "oldContent": "                Uri.parse("}, {"type": "INSERT", "lineNumber": 93, "content": "            onLoadStop: (controller, url) async {"}, {"type": "INSERT", "lineNumber": 94, "content": "              await appConfig.addTokenToLogin(controller: controller);"}, {"type": "INSERT", "lineNumber": 95, "content": "            },"}, {"type": "INSERT", "lineNumber": 96, "content": "            onProgressChanged: (controller, progress) async {"}, {"type": "INSERT", "lineNumber": 97, "content": "              if (progress == 100) {"}, {"type": "INSERT", "lineNumber": 98, "content": "                await appConfig.addTokenToLogin(controller: controller);"}, {"type": "INSERT", "lineNumber": 99, "content": "              }"}, {"type": "INSERT", "lineNumber": 100, "content": "            },"}, {"type": "INSERT", "lineNumber": 101, "content": "            onUpdateVisitedHistory: (controller, url, androidIsReload) async {"}, {"type": "INSERT", "lineNumber": 102, "content": "              await appConfig.addTokenToLogin(controller: controller);"}, {"type": "INSERT", "lineNumber": 103, "content": "            },"}, {"type": "INSERT", "lineNumber": 104, "content": "            onDownloadStartRequest: onDownloadStart,"}, {"type": "INSERT", "lineNumber": 105, "content": "            onReceivedServerTrustAuthRequest: (controller, challenge) async {"}, {"type": "INSERT", "lineNumber": 106, "content": "              return ServerTrustAuthResponse("}, {"type": "INSERT", "lineNumber": 107, "content": "                  action: ServerTrustAuthResponseAction.PROCEED);"}, {"type": "INSERT", "lineNumber": 108, "content": "            },"}, {"type": "INSERT", "lineNumber": 109, "content": "            "}, {"type": "INSERT", "lineNumber": 110, "content": "            initialUrlRequest: URLRequest("}, {"type": "INSERT", "lineNumber": 111, "content": "              url: WebUri.uri("}, {"type": "INSERT", "lineNumber": 112, "content": "                Uri.parse("}]}, {"timestamp": 1758013064848, "changes": [{"type": "MODIFY", "lineNumber": 35, "content": "", "oldContent": "  "}, {"type": "DELETE", "lineNumber": 54, "oldContent": "          context.showBarMessage("}, {"type": "DELETE", "lineNumber": 55, "oldContent": "              isArabic ? \"...جار ي الفتح\" : \"פותח את הקובץ...\");"}, {"type": "INSERT", "lineNumber": 54, "content": "          context"}, {"type": "INSERT", "lineNumber": 55, "content": "              .showBarMessage(isArabic ? \"...جار ي الفتح\" : \"פותח את הקובץ...\");"}, {"type": "MODIFY", "lineNumber": 57, "content": "          downloadFiles(context, controller: controller);", "oldContent": "          downloadFiles(context,controller: controller);"}, {"type": "MODIFY", "lineNumber": 76, "content": "                  true, // Ensure localStorage/sessionStorage work", "oldContent": "              true, // Ensure localStorage/sessionStorage work"}, {"type": "DELETE", "lineNumber": 83, "oldContent": "            onLoadStop: (controller, url) async {"}, {"type": "INSERT", "lineNumber": 84, "content": "                  settings: InAppWebViewSettings("}, {"type": "INSERT", "lineNumber": 85, "content": "                cacheEnabled: false,"}, {"type": "INSERT", "lineNumber": 86, "content": "                clearCache: true,"}, {"type": "INSERT", "lineNumber": 87, "content": "                domStorageEnabled: true,"}, {"type": "INSERT", "lineNumber": 88, "content": "              ));"}, {"type": "INSERT", "lineNumber": 89, "content": ""}, {"type": "INSERT", "lineNumber": 90, "content": "              // Force reload to get latest content"}, {"type": "INSERT", "lineNumber": 91, "content": "              await _forceReload();"}, {"type": "INSERT", "lineNumber": 92, "content": "            },"}, {"type": "INSERT", "lineNumber": 93, "content": "            onLoadStop: (controller, url) async {"}, {"type": "DELETE", "lineNumber": 87, "oldContent": "                  settings: InAppWebViewSettings("}, {"type": "DELETE", "lineNumber": 90, "oldContent": "                    cacheEnabled: false,"}, {"type": "DELETE", "lineNumber": 93, "oldContent": "                    clearCache: true,"}, {"type": "DELETE", "lineNumber": 96, "oldContent": "                    domStorageEnabled: true,"}, {"type": "DELETE", "lineNumber": 99, "oldContent": "                  ));"}, {"type": "DELETE", "lineNumber": 102, "oldContent": ""}, {"type": "DELETE", "lineNumber": 105, "oldContent": "              // Force reload to get latest content"}, {"type": "DELETE", "lineNumber": 107, "oldContent": "            "}, {"type": "DELETE", "lineNumber": 108, "oldContent": "              await _forceReload();"}, {"type": "DELETE", "lineNumber": 111, "oldContent": "            },"}, {"type": "DELETE", "lineNumber": 113, "oldContent": "                  \"${AppConstants.appUrl}?v=${DateTime.now().millisecondsSinceEpoch}\""}, {"type": "DELETE", "lineNumber": 114, "oldContent": "                ),"}, {"type": "INSERT", "lineNumber": 112, "content": "                    \"${AppConstants.appUrl}?v=${DateTime.now().millisecondsSinceEpoch}\"),"}]}, {"timestamp": 1758013104443, "changes": [{"type": "MODIFY", "lineNumber": 84, "content": "                  settings: InAppWebViewSettings(", "oldContent": "                  settings: InAppWebViewSettings("}, {"type": "DELETE", "lineNumber": 86, "oldContent": "              await appConfig.addTokenToLogin(controller: controller);"}, {"type": "DELETE", "lineNumber": 88, "oldContent": "            },"}, {"type": "DELETE", "lineNumber": 91, "oldContent": "            onProgressChanged: (controller, progress) async {"}, {"type": "DELETE", "lineNumber": 93, "oldContent": "              if (progress == 100) {"}, {"type": "DELETE", "lineNumber": 96, "oldContent": "                await appConfig.addTokenToLogin(controller: controller);"}, {"type": "DELETE", "lineNumber": 98, "oldContent": "              }"}, {"type": "INSERT", "lineNumber": 94, "content": "              await appConfig.addTokenToLogin(controller: controller);"}, {"type": "INSERT", "lineNumber": 96, "content": "            onProgressChanged: (controller, progress) async {"}, {"type": "INSERT", "lineNumber": 97, "content": "              if (progress == 100) {"}, {"type": "INSERT", "lineNumber": 98, "content": "                await appConfig.addTokenToLogin(controller: controller);"}, {"type": "INSERT", "lineNumber": 99, "content": "              }"}, {"type": "INSERT", "lineNumber": 100, "content": "            },"}, {"type": "MODIFY", "lineNumber": 112, "content": "                    \"${AppConstants.appUrl}?v=${DateTime.now().millisecondsSinceEpoch}\"),", "oldContent": "                    \"${AppConstants.appUrl}?v=${DateTime.now().millisecondsSinceEpoch}\"),"}]}, {"timestamp": 1758013113429, "changes": [{"type": "INSERT", "lineNumber": 83, "content": "              await controller.setSettings("}, {"type": "DELETE", "lineNumber": 84, "oldContent": "                  settings: InAppWebViewSettings("}, {"type": "DELETE", "lineNumber": 90, "oldContent": "              await appConfig.addTokenToLogin(controller: controller);"}, {"type": "INSERT", "lineNumber": 92, "content": "            },"}, {"type": "INSERT", "lineNumber": 93, "content": "            onLoadStop: (controller, url) async {"}, {"type": "INSERT", "lineNumber": 94, "content": "              await appConfig.addTokenToLogin(controller: controller);"}, {"type": "INSERT", "lineNumber": 95, "content": "            },"}, {"type": "DELETE", "lineNumber": 95, "oldContent": "            },"}, {"type": "DELETE", "lineNumber": 98, "oldContent": "            onLoadStop: (controller, url) async {"}, {"type": "DELETE", "lineNumber": 100, "oldContent": "            },"}, {"type": "INSERT", "lineNumber": 111, "content": "                Uri.parse("}, {"type": "DELETE", "lineNumber": 112, "oldContent": "                    \"${AppConstants.appUrl}?v=${DateTime.now().millisecondsSinceEpoch}\"),"}]}, {"timestamp": 1758013138007, "changes": [{"type": "MODIFY", "lineNumber": 92, "content": "            },", "oldContent": "            },"}, {"type": "DELETE", "lineNumber": 94, "oldContent": "            onProgressChanged: (controller, progress) async {"}, {"type": "MODIFY", "lineNumber": 96, "content": "            onProgressChanged: (controller, progress) async {", "oldContent": "              if (progress == 100) {"}, {"type": "INSERT", "lineNumber": 97, "content": "              if (progress == 100) {"}]}, {"timestamp": 1758013143163, "changes": [{"type": "INSERT", "lineNumber": 91, "content": "              await _forceReload();"}, {"type": "DELETE", "lineNumber": 92, "oldContent": "            },"}, {"type": "INSERT", "lineNumber": 95, "content": "            },"}, {"type": "DELETE", "lineNumber": 97, "oldContent": "            },"}]}, {"timestamp": 1758110151477, "changes": [{"type": "INSERT", "lineNumber": 0, "content": "import 'dart:io';"}, {"type": "INSERT", "lineNumber": 3, "content": "import 'package:image_picker/image_picker.dart';"}]}, {"timestamp": 1758110165458, "changes": [{"type": "MODIFY", "lineNumber": 4, "content": "import 'package:provider/provider.dart';", "oldContent": "import 'package:provider/provider.dart';"}, {"type": "INSERT", "lineNumber": 21, "content": "  final ImagePicker _picker = ImagePicker();"}, {"type": "INSERT", "lineNumber": 39, "content": "  // Handle file upload for WebView"}, {"type": "INSERT", "lineNumber": 40, "content": "  Future<List<String>> _handleFileUpload() async {"}, {"type": "INSERT", "lineNumber": 41, "content": "    try {"}, {"type": "INSERT", "lineNumber": 42, "content": "      // Show options for camera or gallery"}, {"type": "INSERT", "lineNumber": 43, "content": "      final result = await showModalBottomSheet<ImageSource>("}, {"type": "INSERT", "lineNumber": 44, "content": "        context: context,"}, {"type": "INSERT", "lineNumber": 45, "content": "        builder: (BuildContext context) {"}, {"type": "INSERT", "lineNumber": 46, "content": "          return SafeArea("}, {"type": "INSERT", "lineNumber": 47, "content": "            child: <PERSON><PERSON>("}, {"type": "INSERT", "lineNumber": 48, "content": "              children: <Widget>["}, {"type": "INSERT", "lineNumber": 49, "content": "                ListTile("}, {"type": "INSERT", "lineNumber": 50, "content": "                  leading: const Icon(Icons.photo_library),"}, {"type": "INSERT", "lineNumber": 51, "content": "                  title: const Text('Gallery'),"}, {"type": "INSERT", "lineNumber": 52, "content": "                  onTap: () => Navigator.of(context).pop(ImageSource.gallery),"}, {"type": "INSERT", "lineNumber": 53, "content": "                ),"}, {"type": "INSERT", "lineNumber": 54, "content": "                ListTile("}, {"type": "INSERT", "lineNumber": 55, "content": "                  leading: const I<PERSON>(Icons.photo_camera),"}, {"type": "INSERT", "lineNumber": 56, "content": "                  title: const Text('Camera'),"}, {"type": "INSERT", "lineNumber": 57, "content": "                  onTap: () => Navigator.of(context).pop(ImageSource.camera),"}, {"type": "INSERT", "lineNumber": 58, "content": "                ),"}, {"type": "INSERT", "lineNumber": 59, "content": "                ListTile("}, {"type": "INSERT", "lineNumber": 60, "content": "                  leading: const <PERSON><PERSON>(Icons.cancel),"}, {"type": "INSERT", "lineNumber": 61, "content": "                  title: const Text('Cancel'),"}, {"type": "INSERT", "lineNumber": 62, "content": "                  onTap: () => Navigator.of(context).pop(),"}, {"type": "INSERT", "lineNumber": 63, "content": "                ),"}, {"type": "INSERT", "lineNumber": 64, "content": "              ],"}, {"type": "INSERT", "lineNumber": 65, "content": "            ),"}, {"type": "INSERT", "lineNumber": 66, "content": "          );"}, {"type": "INSERT", "lineNumber": 67, "content": "        },"}, {"type": "INSERT", "lineNumber": 68, "content": "      );"}, {"type": "INSERT", "lineNumber": 69, "content": ""}, {"type": "INSERT", "lineNumber": 70, "content": "      if (result != null) {"}, {"type": "INSERT", "lineNumber": 71, "content": "        final XFile? pickedFile = await _picker.pickImage("}, {"type": "INSERT", "lineNumber": 72, "content": "          source: result,"}, {"type": "INSERT", "lineNumber": 73, "content": "          maxWidth: 1920,"}, {"type": "INSERT", "lineNumber": 74, "content": "          maxHeight: 1080,"}, {"type": "INSERT", "lineNumber": 75, "content": "          imageQuality: 85,"}, {"type": "INSERT", "lineNumber": 76, "content": "        );"}, {"type": "INSERT", "lineNumber": 77, "content": ""}, {"type": "INSERT", "lineNumber": 78, "content": "        if (pickedFile != null) {"}, {"type": "INSERT", "lineNumber": 79, "content": "          Log.i('File picked: ${pickedFile.path}');"}, {"type": "INSERT", "lineNumber": 80, "content": "          return [pickedFile.path];"}, {"type": "INSERT", "lineNumber": 81, "content": "        }"}, {"type": "INSERT", "lineNumber": 82, "content": "      }"}, {"type": "INSERT", "lineNumber": 83, "content": "    } catch (e) {"}, {"type": "INSERT", "lineNumber": 84, "content": "      Log.e('Error picking file: $e');"}, {"type": "INSERT", "lineNumber": 85, "content": "    }"}, {"type": "INSERT", "lineNumber": 86, "content": "    return [];"}, {"type": "INSERT", "lineNumber": 87, "content": "  }"}, {"type": "INSERT", "lineNumber": 88, "content": ""}]}, {"timestamp": 1758110178945, "changes": [{"type": "INSERT", "lineNumber": 3, "content": "import 'package:image_picker/image_picker.dart';"}, {"type": "DELETE", "lineNumber": 4, "oldContent": "import 'package:provider/provider.dart';"}, {"type": "DELETE", "lineNumber": 39, "oldContent": "  @override"}, {"type": "DELETE", "lineNumber": 41, "oldContent": "  Widget build(BuildContext context) {"}, {"type": "DELETE", "lineNumber": 43, "oldContent": "    void onDownloadStart(controller, request) async {"}, {"type": "DELETE", "lineNumber": 45, "oldContent": "      Log.i(\"onDownloadStartSSSS $request\");"}, {"type": "DELETE", "lineNumber": 47, "oldContent": ""}, {"type": "DELETE", "lineNumber": 49, "oldContent": "      final data = await controller.evaluateJavascript("}, {"type": "DELETE", "lineNumber": 51, "oldContent": "          source: \"window.localStorage.getItem('UPSTlink')\");"}, {"type": "DELETE", "lineNumber": 53, "oldContent": ""}, {"type": "DELETE", "lineNumber": 55, "oldContent": "      Log.i(\"onDownloadStartSSSS444 $data\");"}, {"type": "DELETE", "lineNumber": 57, "oldContent": ""}, {"type": "DELETE", "lineNumber": 59, "oldContent": "      try {"}, {"type": "DELETE", "lineNumber": 61, "oldContent": "        await prepareSaveDir();"}, {"type": "DELETE", "lineNumber": 63, "oldContent": "        {"}, {"type": "DELETE", "lineNumber": 65, "oldContent": "          final lang = await controller.evaluateJavascript("}, {"type": "DELETE", "lineNumber": 67, "oldContent": "              source: \"window.localStorage.getItem('UPSTlang')\");"}, {"type": "DELETE", "lineNumber": 69, "oldContent": ""}, {"type": "DELETE", "lineNumber": 71, "oldContent": "          final isArabic = lang.toString().contains(\"ar\");"}, {"type": "DELETE", "lineNumber": 73, "oldContent": ""}, {"type": "DELETE", "lineNumber": 75, "oldContent": "          context"}, {"type": "DELETE", "lineNumber": 77, "oldContent": "              .showBarMessage(isArabic ? \"...جار ي الفتح\" : \"פותח את הקובץ...\");"}, {"type": "DELETE", "lineNumber": 79, "oldContent": ""}, {"type": "DELETE", "lineNumber": 81, "oldContent": "          downloadFiles(context, controller: controller);"}, {"type": "DELETE", "lineNumber": 83, "oldContent": "        }"}, {"type": "DELETE", "lineNumber": 85, "oldContent": "      } catch (e) {"}, {"type": "DELETE", "lineNumber": 87, "oldContent": "        Log.e('DownloadError: $e');"}, {"type": "DELETE", "lineNumber": 89, "oldContent": "      }"}, {"type": "DELETE", "lineNumber": 91, "oldContent": "    }"}, {"type": "DELETE", "lineNumber": 93, "oldContent": ""}, {"type": "DELETE", "lineNumber": 95, "oldContent": "    return Scaffold(body: <PERSON><PERSON><PERSON>("}, {"type": "DELETE", "lineNumber": 97, "oldContent": "      child: Consumer<AppConfig>("}, {"type": "DELETE", "lineNumber": 99, "oldContent": "        builder: (context, appConfig, child) {"}, {"type": "DELETE", "lineNumber": 101, "oldContent": "          if (!appConfig.hasInternet) {"}, {"type": "DELETE", "lineNumber": 103, "oldContent": "            return const NoInternetConnectionWidget();"}, {"type": "DELETE", "lineNumber": 105, "oldContent": "          }"}, {"type": "DELETE", "lineNumber": 107, "oldContent": ""}, {"type": "DELETE", "lineNumber": 109, "oldContent": "          return InAppWebView("}, {"type": "DELETE", "lineNumber": 111, "oldContent": "            initialSettings: InAppWebViewSettings("}, {"type": "DELETE", "lineNumber": 113, "oldContent": "              cacheEnabled: false,"}, {"type": "DELETE", "lineNumber": 115, "oldContent": "              clearCache: true,"}, {"type": "DELETE", "lineNumber": 117, "oldContent": "              domStorageEnabled:"}, {"type": "DELETE", "lineNumber": 119, "oldContent": "                  true, // Ensure localStorage/sessionStorage work"}, {"type": "DELETE", "lineNumber": 121, "oldContent": "            ),"}, {"type": "DELETE", "lineNumber": 123, "oldContent": "            onWebViewCreated: (controller) async {"}, {"type": "DELETE", "lineNumber": 125, "oldContent": "              webViewController = controller;"}, {"type": "DELETE", "lineNumber": 127, "oldContent": "              await appConfig.onWebViewCreated(controller);"}, {"type": "DELETE", "lineNumber": 129, "oldContent": ""}, {"type": "DELETE", "lineNumber": 131, "oldContent": "              // Configure cache settings to disable caching but preserve sessions"}, {"type": "DELETE", "lineNumber": 133, "oldContent": "              await controller.setSettings("}, {"type": "DELETE", "lineNumber": 135, "oldContent": "                  settings: InAppWebViewSettings("}, {"type": "DELETE", "lineNumber": 137, "oldContent": "                cacheEnabled: false,"}, {"type": "INSERT", "lineNumber": 89, "content": "  @override"}, {"type": "INSERT", "lineNumber": 90, "content": "  Widget build(BuildContext context) {"}, {"type": "INSERT", "lineNumber": 91, "content": "    void onDownloadStart(controller, request) async {"}, {"type": "INSERT", "lineNumber": 92, "content": "      Log.i(\"onDownloadStartSSSS $request\");"}, {"type": "INSERT", "lineNumber": 93, "content": ""}, {"type": "INSERT", "lineNumber": 94, "content": "      final data = await controller.evaluateJavascript("}, {"type": "INSERT", "lineNumber": 95, "content": "          source: \"window.localStorage.getItem('UPSTlink')\");"}, {"type": "INSERT", "lineNumber": 96, "content": ""}, {"type": "INSERT", "lineNumber": 97, "content": "      Log.i(\"onDownloadStartSSSS444 $data\");"}, {"type": "INSERT", "lineNumber": 98, "content": ""}, {"type": "INSERT", "lineNumber": 99, "content": "      try {"}, {"type": "INSERT", "lineNumber": 100, "content": "        await prepareSaveDir();"}, {"type": "INSERT", "lineNumber": 101, "content": "        {"}, {"type": "INSERT", "lineNumber": 102, "content": "          final lang = await controller.evaluateJavascript("}, {"type": "INSERT", "lineNumber": 103, "content": "              source: \"window.localStorage.getItem('UPSTlang')\");"}, {"type": "INSERT", "lineNumber": 104, "content": ""}, {"type": "INSERT", "lineNumber": 105, "content": "          final isArabic = lang.toString().contains(\"ar\");"}, {"type": "INSERT", "lineNumber": 106, "content": ""}, {"type": "INSERT", "lineNumber": 107, "content": "          context"}, {"type": "INSERT", "lineNumber": 108, "content": "              .showBarMessage(isArabic ? \"...جار ي الفتح\" : \"פותח את הקובץ...\");"}, {"type": "INSERT", "lineNumber": 109, "content": ""}, {"type": "INSERT", "lineNumber": 110, "content": "          downloadFiles(context, controller: controller);"}, {"type": "INSERT", "lineNumber": 111, "content": "        }"}, {"type": "INSERT", "lineNumber": 112, "content": "      } catch (e) {"}, {"type": "INSERT", "lineNumber": 113, "content": "        Log.e('DownloadError: $e');"}, {"type": "INSERT", "lineNumber": 114, "content": "      }"}, {"type": "INSERT", "lineNumber": 115, "content": "    }"}, {"type": "INSERT", "lineNumber": 116, "content": ""}, {"type": "INSERT", "lineNumber": 117, "content": "    return Scaffold(body: <PERSON><PERSON><PERSON>("}, {"type": "INSERT", "lineNumber": 118, "content": "      child: Consumer<AppConfig>("}, {"type": "INSERT", "lineNumber": 119, "content": "        builder: (context, appConfig, child) {"}, {"type": "INSERT", "lineNumber": 120, "content": "          if (!appConfig.hasInternet) {"}, {"type": "INSERT", "lineNumber": 121, "content": "            return const NoInternetConnectionWidget();"}, {"type": "INSERT", "lineNumber": 122, "content": "          }"}, {"type": "INSERT", "lineNumber": 123, "content": ""}, {"type": "INSERT", "lineNumber": 124, "content": "          return InAppWebView("}, {"type": "INSERT", "lineNumber": 125, "content": "            initialSettings: InAppWebViewSettings("}, {"type": "INSERT", "lineNumber": 126, "content": "              cacheEnabled: false,"}, {"type": "INSERT", "lineNumber": 127, "content": "              clearCache: true,"}, {"type": "INSERT", "lineNumber": 128, "content": "              domStorageEnabled:"}, {"type": "INSERT", "lineNumber": 129, "content": "                  true, // Ensure localStorage/sessionStorage work"}, {"type": "INSERT", "lineNumber": 130, "content": "              allowFileAccessFromFileURLs: true,"}, {"type": "INSERT", "lineNumber": 131, "content": "              allowUniversalAccessFromFileURLs: true,"}, {"type": "INSERT", "lineNumber": 132, "content": "              mediaPlaybackRequiresUserGesture: false,"}, {"type": "INSERT", "lineNumber": 133, "content": "            ),"}, {"type": "INSERT", "lineNumber": 134, "content": "            onWebViewCreated: (controller) async {"}, {"type": "INSERT", "lineNumber": 135, "content": "              webViewController = controller;"}, {"type": "INSERT", "lineNumber": 136, "content": "              await appConfig.onWebViewCreated(controller);"}, {"type": "INSERT", "lineNumber": 137, "content": ""}, {"type": "INSERT", "lineNumber": 138, "content": "              // Configure cache settings to disable caching but preserve sessions"}, {"type": "INSERT", "lineNumber": 139, "content": "              await controller.setSettings("}, {"type": "INSERT", "lineNumber": 140, "content": "                  settings: InAppWebViewSettings("}, {"type": "INSERT", "lineNumber": 141, "content": "                cacheEnabled: false,"}, {"type": "INSERT", "lineNumber": 144, "content": "                allowFileAccessFromFileURLs: true,"}, {"type": "INSERT", "lineNumber": 145, "content": "                allowUniversalAccessFromFileURLs: true,"}, {"type": "INSERT", "lineNumber": 146, "content": "                mediaPlaybackRequiresUserGesture: false,"}, {"type": "INSERT", "lineNumber": 152, "content": "            // Handle file upload requests"}, {"type": "INSERT", "lineNumber": 153, "content": "            onShowFileChooser: (controller, fileChooserParams) async {"}, {"type": "INSERT", "lineNumber": 154, "content": "              Log.i('File chooser triggered: ${fileChooserParams.toString()}');"}, {"type": "INSERT", "lineNumber": 155, "content": ""}, {"type": "INSERT", "lineNumber": 156, "content": "              try {"}, {"type": "INSERT", "lineNumber": 157, "content": "                final filePaths = await _handleFileUpload();"}, {"type": "INSERT", "lineNumber": 158, "content": ""}, {"type": "INSERT", "lineNumber": 159, "content": "                if (filePaths.isNotEmpty) {"}, {"type": "INSERT", "lineNumber": 160, "content": "                  Log.i('Returning file paths: $filePaths');"}, {"type": "INSERT", "lineNumber": 161, "content": "                  return filePaths;"}, {"type": "INSERT", "lineNumber": 162, "content": "                }"}, {"type": "INSERT", "lineNumber": 163, "content": "              } catch (e) {"}, {"type": "INSERT", "lineNumber": 164, "content": "                Log.e('Error in file chooser: $e');"}, {"type": "INSERT", "lineNumber": 165, "content": "              }"}, {"type": "INSERT", "lineNumber": 166, "content": ""}, {"type": "INSERT", "lineNumber": 167, "content": "              // Return empty list if no file selected or error occurred"}, {"type": "INSERT", "lineNumber": 168, "content": "              return [];"}, {"type": "INSERT", "lineNumber": 169, "content": "            },"}]}, {"timestamp": 1758110307822, "changes": [{"type": "INSERT", "lineNumber": 64, "content": "              ],"}, {"type": "INSERT", "lineNumber": 65, "content": "            ),"}, {"type": "INSERT", "lineNumber": 66, "content": "          );"}, {"type": "INSERT", "lineNumber": 67, "content": "        },"}, {"type": "INSERT", "lineNumber": 68, "content": "      );"}, {"type": "INSERT", "lineNumber": 69, "content": ""}, {"type": "INSERT", "lineNumber": 70, "content": "      if (result != null) {"}, {"type": "INSERT", "lineNumber": 71, "content": "        final XFile? pickedFile = await _picker.pickImage("}, {"type": "INSERT", "lineNumber": 72, "content": "          source: result,"}, {"type": "INSERT", "lineNumber": 73, "content": "          maxWidth: 1920,"}, {"type": "INSERT", "lineNumber": 74, "content": "          maxHeight: 1080,"}, {"type": "INSERT", "lineNumber": 75, "content": "          imageQuality: 85,"}, {"type": "INSERT", "lineNumber": 76, "content": "        );"}, {"type": "INSERT", "lineNumber": 77, "content": ""}, {"type": "INSERT", "lineNumber": 78, "content": "        if (pickedFile != null) {"}, {"type": "INSERT", "lineNumber": 79, "content": "          Log.i('File picked: ${pickedFile.path}');"}, {"type": "INSERT", "lineNumber": 80, "content": "          return [pickedFile.path];"}, {"type": "INSERT", "lineNumber": 81, "content": "        }"}, {"type": "INSERT", "lineNumber": 82, "content": "      }"}, {"type": "INSERT", "lineNumber": 83, "content": "    } catch (e) {"}, {"type": "INSERT", "lineNumber": 84, "content": "      Log.e('Error picking file: $e');"}, {"type": "INSERT", "lineNumber": 85, "content": "    }"}, {"type": "INSERT", "lineNumber": 86, "content": "    return [];"}, {"type": "INSERT", "lineNumber": 87, "content": "  }"}, {"type": "INSERT", "lineNumber": 88, "content": ""}, {"type": "DELETE", "lineNumber": 66, "oldContent": "              ],"}, {"type": "DELETE", "lineNumber": 69, "oldContent": "            ),"}, {"type": "DELETE", "lineNumber": 72, "oldContent": "          );"}, {"type": "DELETE", "lineNumber": 75, "oldContent": "        },"}, {"type": "DELETE", "lineNumber": 78, "oldContent": "      );"}, {"type": "DELETE", "lineNumber": 81, "oldContent": ""}, {"type": "DELETE", "lineNumber": 84, "oldContent": "      if (result != null) {"}, {"type": "DELETE", "lineNumber": 87, "oldContent": "        final XFile? pickedFile = await _picker.pickImage("}, {"type": "DELETE", "lineNumber": 90, "oldContent": "          source: result,"}, {"type": "DELETE", "lineNumber": 93, "oldContent": "          maxWidth: 1920,"}, {"type": "DELETE", "lineNumber": 96, "oldContent": "          maxHeight: 1080,"}, {"type": "DELETE", "lineNumber": 99, "oldContent": "          imageQuality: 85,"}, {"type": "DELETE", "lineNumber": 102, "oldContent": "        );"}, {"type": "DELETE", "lineNumber": 105, "oldContent": ""}, {"type": "DELETE", "lineNumber": 108, "oldContent": "        if (pickedFile != null) {"}, {"type": "DELETE", "lineNumber": 111, "oldContent": "          Log.i('File picked: ${pickedFile.path}');"}, {"type": "DELETE", "lineNumber": 114, "oldContent": "          return [pickedFile.path];"}, {"type": "DELETE", "lineNumber": 117, "oldContent": "        }"}, {"type": "DELETE", "lineNumber": 120, "oldContent": "      }"}, {"type": "DELETE", "lineNumber": 123, "oldContent": "    } catch (e) {"}, {"type": "DELETE", "lineNumber": 126, "oldContent": "      Log.e('Error picking file: $e');"}, {"type": "DELETE", "lineNumber": 129, "oldContent": "    }"}, {"type": "DELETE", "lineNumber": 132, "oldContent": "    return [];"}, {"type": "DELETE", "lineNumber": 135, "oldContent": "  }"}, {"type": "DELETE", "lineNumber": 138, "oldContent": ""}, {"type": "DELETE", "lineNumber": 140, "oldContent": "                clearCache: true,"}, {"type": "MODIFY", "lineNumber": 142, "content": "                clearCache: true,", "oldContent": "                domStorageEnabled: true,"}, {"type": "INSERT", "lineNumber": 143, "content": "                domStorageEnabled: true,"}, {"type": "INSERT", "lineNumber": 144, "content": "                allowFileAccessFromFileURLs: true,"}, {"type": "INSERT", "lineNumber": 145, "content": "                allowUniversalAccessFromFileURLs: true,"}, {"type": "INSERT", "lineNumber": 146, "content": "                mediaPlaybackRequiresUserGesture: false,"}, {"type": "DELETE", "lineNumber": 147, "oldContent": "                allowFileAccessFromFileURLs: true,"}, {"type": "DELETE", "lineNumber": 149, "oldContent": "                allowUniversalAccessFromFileURLs: true,"}, {"type": "INSERT", "lineNumber": 152, "content": "            // Add JavaScript handlers for file upload"}, {"type": "INSERT", "lineNumber": 153, "content": "            onWebViewCreated: (controller) async {"}, {"type": "INSERT", "lineNumber": 154, "content": "              webViewController = controller;"}, {"type": "INSERT", "lineNumber": 155, "content": "              await appConfig.onWebViewCreated(controller);"}, {"type": "INSERT", "lineNumber": 156, "content": ""}, {"type": "INSERT", "lineNumber": 157, "content": "              // Configure cache settings to disable caching but preserve sessions"}, {"type": "INSERT", "lineNumber": 158, "content": "              await controller.setSettings("}, {"type": "INSERT", "lineNumber": 159, "content": "                  settings: InAppWebViewSettings("}, {"type": "INSERT", "lineNumber": 160, "content": "                cacheEnabled: false,"}, {"type": "INSERT", "lineNumber": 161, "content": "                clearCache: true,"}, {"type": "INSERT", "lineNumber": 162, "content": "                domStorageEnabled: true,"}, {"type": "INSERT", "lineNumber": 163, "content": "                allowFileAccessFromFileURLs: true,"}, {"type": "INSERT", "lineNumber": 164, "content": "                allowUniversalAccessFromFileURLs: true,"}, {"type": "INSERT", "lineNumber": 166, "content": "              ));"}, {"type": "INSERT", "lineNumber": 167, "content": ""}, {"type": "INSERT", "lineNumber": 168, "content": "              // Add JavaScript handler for file upload"}, {"type": "INSERT", "lineNumber": 169, "content": "              await controller.addJavaScriptHandler("}, {"type": "INSERT", "lineNumber": 170, "content": "                handlerName: 'fileUploadHandler',"}, {"type": "INSERT", "lineNumber": 171, "content": "                callback: (args) async {"}, {"type": "INSERT", "lineNumber": 172, "content": "                  Log.i('File upload handler called');"}, {"type": "INSERT", "lineNumber": 173, "content": "                  try {"}, {"type": "INSERT", "lineNumber": 174, "content": "                    final filePaths = await _handleFileUpload();"}, {"type": "INSERT", "lineNumber": 175, "content": "                    if (filePaths.isNotEmpty) {"}, {"type": "INSERT", "lineNumber": 176, "content": "                      return {'success': true, 'filePath': filePaths.first};"}, {"type": "INSERT", "lineNumber": 177, "content": "                    }"}, {"type": "INSERT", "lineNumber": 178, "content": "                    return {'success': false, 'error': 'No file selected'};"}, {"type": "INSERT", "lineNumber": 179, "content": "                  } catch (e) {"}, {"type": "INSERT", "lineNumber": 180, "content": "                    Log.e('Error in file upload handler: $e');"}, {"type": "INSERT", "lineNumber": 181, "content": "                    return {'success': false, 'error': e.toString()};"}, {"type": "INSERT", "lineNumber": 182, "content": "                  }"}, {"type": "INSERT", "lineNumber": 183, "content": "                },"}, {"type": "INSERT", "lineNumber": 184, "content": "              );"}, {"type": "INSERT", "lineNumber": 185, "content": ""}, {"type": "INSERT", "lineNumber": 186, "content": "              // Force reload to get latest content"}, {"type": "INSERT", "lineNumber": 187, "content": "              await _forceReload();"}, {"type": "INSERT", "lineNumber": 188, "content": "            },"}, {"type": "DELETE", "lineNumber": 158, "oldContent": "            // Handle file upload requests"}, {"type": "DELETE", "lineNumber": 160, "oldContent": "            onShowFileChooser: (controller, fileChooserParams) async {"}, {"type": "DELETE", "lineNumber": 162, "oldContent": "              Log.i('File chooser triggered: ${fileChooserParams.toString()}');"}, {"type": "DELETE", "lineNumber": 164, "oldContent": ""}, {"type": "DELETE", "lineNumber": 166, "oldContent": "              try {"}, {"type": "DELETE", "lineNumber": 168, "oldContent": "                final filePaths = await _handleFileUpload();"}, {"type": "DELETE", "lineNumber": 170, "oldContent": ""}, {"type": "DELETE", "lineNumber": 172, "oldContent": "                if (filePaths.isNotEmpty) {"}, {"type": "DELETE", "lineNumber": 174, "oldContent": "                  Log.i('Returning file paths: $filePaths');"}, {"type": "DELETE", "lineNumber": 176, "oldContent": "                  return filePaths;"}, {"type": "DELETE", "lineNumber": 178, "oldContent": "                }"}, {"type": "DELETE", "lineNumber": 180, "oldContent": "              } catch (e) {"}, {"type": "DELETE", "lineNumber": 182, "oldContent": "                Log.e('Error in file chooser: $e');"}, {"type": "DELETE", "lineNumber": 184, "oldContent": "              }"}, {"type": "DELETE", "lineNumber": 186, "oldContent": ""}, {"type": "DELETE", "lineNumber": 188, "oldContent": "              // Return empty list if no file selected or error occurred"}, {"type": "DELETE", "lineNumber": 190, "oldContent": "              return [];"}, {"type": "DELETE", "lineNumber": 192, "oldContent": "            },"}]}, {"timestamp": 1758110317621, "changes": [{"type": "DELETE", "lineNumber": 65, "oldContent": "  @override"}, {"type": "DELETE", "lineNumber": 67, "oldContent": "  Widget build(BuildContext context) {"}, {"type": "DELETE", "lineNumber": 70, "oldContent": "    void onDownloadStart(controller, request) async {"}, {"type": "DELETE", "lineNumber": 72, "oldContent": "      Log.i(\"onDownloadStartSSSS $request\");"}, {"type": "DELETE", "lineNumber": 75, "oldContent": ""}, {"type": "DELETE", "lineNumber": 77, "oldContent": "      final data = await controller.evaluateJavascript("}, {"type": "DELETE", "lineNumber": 80, "oldContent": "          source: \"window.localStorage.getItem('UPSTlink')\");"}, {"type": "DELETE", "lineNumber": 82, "oldContent": ""}, {"type": "DELETE", "lineNumber": 85, "oldContent": "      Log.i(\"onDownloadStartSSSS444 $data\");"}, {"type": "DELETE", "lineNumber": 87, "oldContent": ""}, {"type": "DELETE", "lineNumber": 90, "oldContent": "      try {"}, {"type": "DELETE", "lineNumber": 92, "oldContent": "        await prepareSaveDir();"}, {"type": "DELETE", "lineNumber": 95, "oldContent": "        {"}, {"type": "DELETE", "lineNumber": 97, "oldContent": "          final lang = await controller.evaluateJavascript("}, {"type": "DELETE", "lineNumber": 100, "oldContent": "              source: \"window.localStorage.getItem('UPSTlang')\");"}, {"type": "DELETE", "lineNumber": 102, "oldContent": ""}, {"type": "INSERT", "lineNumber": 89, "content": "  @override"}, {"type": "INSERT", "lineNumber": 90, "content": "  Widget build(BuildContext context) {"}, {"type": "INSERT", "lineNumber": 91, "content": "    void onDownloadStart(controller, request) async {"}, {"type": "INSERT", "lineNumber": 92, "content": "      Log.i(\"onDownloadStartSSSS $request\");"}, {"type": "INSERT", "lineNumber": 93, "content": ""}, {"type": "INSERT", "lineNumber": 94, "content": "      final data = await controller.evaluateJavascript("}, {"type": "INSERT", "lineNumber": 95, "content": "          source: \"window.localStorage.getItem('UPSTlink')\");"}, {"type": "INSERT", "lineNumber": 96, "content": ""}, {"type": "INSERT", "lineNumber": 97, "content": "      Log.i(\"onDownloadStartSSSS444 $data\");"}, {"type": "INSERT", "lineNumber": 98, "content": ""}, {"type": "INSERT", "lineNumber": 99, "content": "      try {"}, {"type": "INSERT", "lineNumber": 100, "content": "        await prepareSaveDir();"}, {"type": "INSERT", "lineNumber": 101, "content": "        {"}, {"type": "INSERT", "lineNumber": 102, "content": "          final lang = await controller.evaluateJavascript("}, {"type": "INSERT", "lineNumber": 103, "content": "              source: \"window.localStorage.getItem('UPSTlang')\");"}, {"type": "INSERT", "lineNumber": 104, "content": ""}, {"type": "DELETE", "lineNumber": 134, "oldContent": "            onWebViewCreated: (controller) async {"}, {"type": "DELETE", "lineNumber": 135, "oldContent": "              webViewController = controller;"}, {"type": "DELETE", "lineNumber": 136, "oldContent": "              await appConfig.onWebViewCreated(controller);"}, {"type": "DELETE", "lineNumber": 138, "oldContent": "              // Configure cache settings to disable caching but preserve sessions"}, {"type": "DELETE", "lineNumber": 139, "oldContent": "              await controller.setSettings("}, {"type": "DELETE", "lineNumber": 140, "oldContent": "                  settings: InAppWebViewSettings("}, {"type": "DELETE", "lineNumber": 141, "oldContent": "                clearCache: true,"}, {"type": "DELETE", "lineNumber": 142, "oldContent": "                domStorageEnabled: true,"}, {"type": "DELETE", "lineNumber": 143, "oldContent": "                cacheEnabled: false,"}, {"type": "DELETE", "lineNumber": 144, "oldContent": "                allowFileAccessFromFileURLs: true,"}, {"type": "DELETE", "lineNumber": 145, "oldContent": "              ));"}, {"type": "DELETE", "lineNumber": 146, "oldContent": "                allowUniversalAccessFromFileURLs: true,"}, {"type": "DELETE", "lineNumber": 147, "oldContent": ""}, {"type": "DELETE", "lineNumber": 148, "oldContent": "                mediaPlaybackRequiresUserGesture: false,"}, {"type": "DELETE", "lineNumber": 149, "oldContent": "              // Force reload to get latest content"}, {"type": "DELETE", "lineNumber": 150, "oldContent": "              await _forceReload();"}, {"type": "DELETE", "lineNumber": 151, "oldContent": "            },"}, {"type": "DELETE", "lineNumber": 152, "oldContent": "                mediaPlaybackRequiresUserGesture: false,"}, {"type": "DELETE", "lineNumber": 154, "oldContent": "            onLoadStop: (controller, url) async {"}, {"type": "DELETE", "lineNumber": 156, "oldContent": "              await appConfig.addTokenToLogin(controller: controller);"}, {"type": "DELETE", "lineNumber": 158, "oldContent": "            },"}, {"type": "DELETE", "lineNumber": 160, "oldContent": "            onProgressChanged: (controller, progress) async {"}, {"type": "DELETE", "lineNumber": 162, "oldContent": "              if (progress == 100) {"}, {"type": "DELETE", "lineNumber": 164, "oldContent": "                await appConfig.addTokenToLogin(controller: controller);"}, {"type": "DELETE", "lineNumber": 167, "oldContent": "              }"}, {"type": "DELETE", "lineNumber": 170, "oldContent": "            },"}, {"type": "DELETE", "lineNumber": 173, "oldContent": "            onUpdateVisitedHistory: (controller, url, androidIsReload) async {"}, {"type": "DELETE", "lineNumber": 175, "oldContent": "              await appConfig.addTokenToLogin(controller: controller);"}, {"type": "INSERT", "lineNumber": 148, "content": "                mediaPlaybackRequiresUserGesture: false,"}, {"type": "DELETE", "lineNumber": 178, "oldContent": "            },"}, {"type": "DELETE", "lineNumber": 181, "oldContent": "            onDownloadStartRequest: onDownloadStart,"}, {"type": "DELETE", "lineNumber": 184, "oldContent": "            onReceivedServerTrustAuthRequest: (controller, challenge) async {"}, {"type": "DELETE", "lineNumber": 187, "oldContent": "              return ServerTrustAuthResponse("}, {"type": "DELETE", "lineNumber": 190, "oldContent": "                  action: ServerTrustAuthResponseAction.PROCEED);"}, {"type": "DELETE", "lineNumber": 193, "oldContent": "            },"}, {"type": "DELETE", "lineNumber": 196, "oldContent": "            initialUrlRequest: URLRequest("}, {"type": "DELETE", "lineNumber": 199, "oldContent": "              url: WebUri.uri("}, {"type": "DELETE", "lineNumber": 202, "oldContent": "                Uri.parse("}, {"type": "DELETE", "lineNumber": 205, "oldContent": "                    \"${AppConstants.appUrl}?v=${DateTime.now().millisecondsSinceEpoch}\"),"}, {"type": "DELETE", "lineNumber": 208, "oldContent": "              ),"}, {"type": "INSERT", "lineNumber": 172, "content": "            onLoadStop: (controller, url) async {"}, {"type": "INSERT", "lineNumber": 173, "content": "              await appConfig.addTokenToLogin(controller: controller);"}, {"type": "INSERT", "lineNumber": 174, "content": "            },"}, {"type": "INSERT", "lineNumber": 175, "content": "            onProgressChanged: (controller, progress) async {"}, {"type": "INSERT", "lineNumber": 176, "content": "              if (progress == 100) {"}, {"type": "INSERT", "lineNumber": 177, "content": "                await appConfig.addTokenToLogin(controller: controller);"}, {"type": "INSERT", "lineNumber": 178, "content": "              }"}, {"type": "INSERT", "lineNumber": 179, "content": "            },"}, {"type": "INSERT", "lineNumber": 180, "content": "            onUpdateVisitedHistory: (controller, url, androidIsReload) async {"}, {"type": "INSERT", "lineNumber": 181, "content": "              await appConfig.addTokenToLogin(controller: controller);"}, {"type": "INSERT", "lineNumber": 182, "content": "            },"}, {"type": "INSERT", "lineNumber": 183, "content": "            onDownloadStartRequest: onDownloadStart,"}, {"type": "INSERT", "lineNumber": 184, "content": "            onReceivedServerTrustAuthRequest: (controller, challenge) async {"}, {"type": "INSERT", "lineNumber": 185, "content": "              return ServerTrustAuthResponse("}, {"type": "INSERT", "lineNumber": 186, "content": "                  action: ServerTrustAuthResponseAction.PROCEED);"}, {"type": "INSERT", "lineNumber": 187, "content": "            },"}, {"type": "INSERT", "lineNumber": 188, "content": "            initialUrlRequest: URLRequest("}, {"type": "INSERT", "lineNumber": 189, "content": "              url: WebUri.uri("}, {"type": "INSERT", "lineNumber": 190, "content": "                Uri.parse("}, {"type": "INSERT", "lineNumber": 191, "content": "                    \"${AppConstants.appUrl}?v=${DateTime.now().millisecondsSinceEpoch}\"),"}, {"type": "INSERT", "lineNumber": 192, "content": "              ),"}]}, {"timestamp": 1758110333730, "changes": [{"type": "DELETE", "lineNumber": 79, "oldContent": "  @override"}, {"type": "INSERT", "lineNumber": 80, "content": "          return [pickedFile.path];"}, {"type": "INSERT", "lineNumber": 81, "content": "        }"}, {"type": "INSERT", "lineNumber": 82, "content": "      }"}, {"type": "INSERT", "lineNumber": 83, "content": "    } catch (e) {"}, {"type": "INSERT", "lineNumber": 84, "content": "      Log.e('Error picking file: $e');"}, {"type": "INSERT", "lineNumber": 85, "content": "    }"}, {"type": "INSERT", "lineNumber": 86, "content": "    return [];"}, {"type": "INSERT", "lineNumber": 87, "content": "  }"}, {"type": "INSERT", "lineNumber": 88, "content": ""}, {"type": "INSERT", "lineNumber": 89, "content": "  @override"}, {"type": "DELETE", "lineNumber": 83, "oldContent": "          return [pickedFile.path];"}, {"type": "DELETE", "lineNumber": 86, "oldContent": "        }"}, {"type": "DELETE", "lineNumber": 88, "oldContent": "      }"}, {"type": "DELETE", "lineNumber": 91, "oldContent": "    } catch (e) {"}, {"type": "DELETE", "lineNumber": 94, "oldContent": "      Log.e('Error picking file: $e');"}, {"type": "DELETE", "lineNumber": 96, "oldContent": "    }"}, {"type": "DELETE", "lineNumber": 99, "oldContent": "    return [];"}, {"type": "DELETE", "lineNumber": 102, "oldContent": "  }"}, {"type": "DELETE", "lineNumber": 104, "oldContent": ""}, {"type": "DELETE", "lineNumber": 135, "oldContent": "                mediaPlaybackRequiresUserGesture: false,"}, {"type": "DELETE", "lineNumber": 147, "oldContent": "            onLoadStop: (controller, url) async {"}, {"type": "DELETE", "lineNumber": 149, "oldContent": "              await appConfig.addTokenToLogin(controller: controller);"}, {"type": "DELETE", "lineNumber": 150, "oldContent": "            },"}, {"type": "DELETE", "lineNumber": 152, "oldContent": "            onProgressChanged: (controller, progress) async {"}, {"type": "DELETE", "lineNumber": 153, "oldContent": "              if (progress == 100) {"}, {"type": "INSERT", "lineNumber": 148, "content": "                mediaPlaybackRequiresUserGesture: false,"}, {"type": "DELETE", "lineNumber": 155, "oldContent": "                await appConfig.addTokenToLogin(controller: controller);"}, {"type": "DELETE", "lineNumber": 157, "oldContent": "              }"}, {"type": "DELETE", "lineNumber": 158, "oldContent": "            },"}, {"type": "DELETE", "lineNumber": 160, "oldContent": "            onUpdateVisitedHistory: (controller, url, androidIsReload) async {"}, {"type": "DELETE", "lineNumber": 162, "oldContent": "              await appConfig.addTokenToLogin(controller: controller);"}, {"type": "DELETE", "lineNumber": 163, "oldContent": "            },"}, {"type": "DELETE", "lineNumber": 165, "oldContent": "            onDownloadStartRequest: onDownloadStart,"}, {"type": "DELETE", "lineNumber": 167, "oldContent": "            onReceivedServerTrustAuthRequest: (controller, challenge) async {"}, {"type": "DELETE", "lineNumber": 168, "oldContent": "              return ServerTrustAuthResponse("}, {"type": "DELETE", "lineNumber": 170, "oldContent": "                  action: ServerTrustAuthResponseAction.PROCEED);"}, {"type": "DELETE", "lineNumber": 172, "oldContent": "            },"}, {"type": "DELETE", "lineNumber": 173, "oldContent": "            initialUrlRequest: URLRequest("}, {"type": "DELETE", "lineNumber": 175, "oldContent": "              url: WebUri.uri("}, {"type": "DELETE", "lineNumber": 177, "oldContent": "                Uri.parse("}, {"type": "DELETE", "lineNumber": 178, "oldContent": "                    \"${AppConstants.appUrl}?v=${DateTime.now().millisecondsSinceEpoch}\"),"}, {"type": "DELETE", "lineNumber": 180, "oldContent": "              ),"}, {"type": "INSERT", "lineNumber": 172, "content": "            onLoadStop: (controller, url) async {"}, {"type": "INSERT", "lineNumber": 173, "content": "              await appConfig.addTokenToLogin(controller: controller);"}, {"type": "INSERT", "lineNumber": 174, "content": ""}, {"type": "INSERT", "lineNumber": 175, "content": "              // Inject JavaScript to handle file input clicks"}, {"type": "INSERT", "lineNumber": 176, "content": "              await controller.evaluateJavascript(source: '''"}, {"type": "INSERT", "lineNumber": 177, "content": "                (function() {"}, {"type": "INSERT", "lineNumber": 178, "content": "                  // Override file input behavior"}, {"type": "INSERT", "lineNumber": 179, "content": "                  function handleFileInputs() {"}, {"type": "INSERT", "lineNumber": 180, "content": "                    const fileInputs = document.querySelectorAll('input[type=\"file\"]');"}, {"type": "INSERT", "lineNumber": 181, "content": "                    fileInputs.forEach(function(input) {"}, {"type": "INSERT", "lineNumber": 182, "content": "                      if (!input.hasAttribute('data-flutter-handled')) {"}, {"type": "INSERT", "lineNumber": 183, "content": "                        input.setAttribute('data-flutter-handled', 'true');"}, {"type": "INSERT", "lineNumber": 184, "content": "                        input.addEventListener('click', function(e) {"}, {"type": "INSERT", "lineNumber": 185, "content": "                          e.prevent<PERSON><PERSON><PERSON>();"}, {"type": "INSERT", "lineNumber": 186, "content": "                          e.stopPropagation();"}, {"type": "INSERT", "lineNumber": 187, "content": ""}, {"type": "INSERT", "lineNumber": 188, "content": "                          // Call Flutter handler"}, {"type": "INSERT", "lineNumber": 189, "content": "                          window.flutter_inappwebview.callHandler('fileUploadHandler').then(function(result) {"}, {"type": "INSERT", "lineNumber": 190, "content": "                            if (result && result.success && result.filePath) {"}, {"type": "INSERT", "lineNumber": 191, "content": "                              // Create a new File object and trigger change event"}, {"type": "INSERT", "lineNumber": 192, "content": "                              const dt = new DataTransfer();"}, {"type": "INSERT", "lineNumber": 193, "content": "                              fetch('file://' + result.filePath)"}, {"type": "INSERT", "lineNumber": 194, "content": "                                .then(response => response.blob())"}, {"type": "INSERT", "lineNumber": 195, "content": "                                .then(blob => {"}, {"type": "INSERT", "lineNumber": 196, "content": "                                  const file = new File([blob], 'image.jpg', { type: 'image/jpeg' });"}, {"type": "INSERT", "lineNumber": 197, "content": "                                  dt.items.add(file);"}, {"type": "INSERT", "lineNumber": 198, "content": "                                  input.files = dt.files;"}, {"type": "INSERT", "lineNumber": 199, "content": ""}, {"type": "INSERT", "lineNumber": 200, "content": "                                  // Trigger change event"}, {"type": "INSERT", "lineNumber": 201, "content": "                                  const changeEvent = new Event('change', { bubbles: true });"}, {"type": "INSERT", "lineNumber": 202, "content": "                                  input.dispatchEvent(changeEvent);"}, {"type": "INSERT", "lineNumber": 203, "content": "                                })"}, {"type": "INSERT", "lineNumber": 204, "content": "                                .catch(error => {"}, {"type": "INSERT", "lineNumber": 205, "content": "                                  console.error('Error creating file:', error);"}, {"type": "INSERT", "lineNumber": 206, "content": "                                });"}, {"type": "INSERT", "lineNumber": 207, "content": "                            }"}, {"type": "INSERT", "lineNumber": 208, "content": "                          }).catch(function(error) {"}, {"type": "INSERT", "lineNumber": 209, "content": "                            console.error('Error calling file upload handler:', error);"}, {"type": "INSERT", "lineNumber": 210, "content": "                          });"}, {"type": "INSERT", "lineNumber": 211, "content": "                        });"}, {"type": "INSERT", "lineNumber": 212, "content": "                      }"}, {"type": "INSERT", "lineNumber": 213, "content": "                    });"}, {"type": "INSERT", "lineNumber": 214, "content": "                  }"}, {"type": "INSERT", "lineNumber": 215, "content": ""}, {"type": "INSERT", "lineNumber": 216, "content": "                  // Handle initial file inputs"}, {"type": "INSERT", "lineNumber": 217, "content": "                  handleFileInputs();"}, {"type": "INSERT", "lineNumber": 218, "content": ""}, {"type": "INSERT", "lineNumber": 219, "content": "                  // Handle dynamically added file inputs"}, {"type": "INSERT", "lineNumber": 220, "content": "                  const observer = new MutationObserver(function(mutations) {"}, {"type": "INSERT", "lineNumber": 221, "content": "                    mutations.forEach(function(mutation) {"}, {"type": "INSERT", "lineNumber": 222, "content": "                      if (mutation.type === 'childList') {"}, {"type": "INSERT", "lineNumber": 223, "content": "                        handleFileInputs();"}, {"type": "INSERT", "lineNumber": 224, "content": "                      }"}, {"type": "INSERT", "lineNumber": 225, "content": "                    });"}, {"type": "INSERT", "lineNumber": 226, "content": "                  });"}, {"type": "INSERT", "lineNumber": 227, "content": ""}, {"type": "INSERT", "lineNumber": 228, "content": "                  observer.observe(document.body, {"}, {"type": "INSERT", "lineNumber": 229, "content": "                    childList: true,"}, {"type": "INSERT", "lineNumber": 230, "content": "                    subtree: true"}, {"type": "INSERT", "lineNumber": 231, "content": "                  });"}, {"type": "INSERT", "lineNumber": 232, "content": "                })();"}, {"type": "INSERT", "lineNumber": 233, "content": "              ''');"}, {"type": "INSERT", "lineNumber": 234, "content": "            },"}, {"type": "INSERT", "lineNumber": 235, "content": "            onProgressChanged: (controller, progress) async {"}, {"type": "INSERT", "lineNumber": 236, "content": "              if (progress == 100) {"}, {"type": "INSERT", "lineNumber": 237, "content": "                await appConfig.addTokenToLogin(controller: controller);"}, {"type": "INSERT", "lineNumber": 238, "content": "              }"}, {"type": "INSERT", "lineNumber": 239, "content": "            },"}, {"type": "INSERT", "lineNumber": 240, "content": "            onUpdateVisitedHistory: (controller, url, androidIsReload) async {"}, {"type": "INSERT", "lineNumber": 241, "content": "              await appConfig.addTokenToLogin(controller: controller);"}, {"type": "INSERT", "lineNumber": 242, "content": "            },"}, {"type": "INSERT", "lineNumber": 243, "content": "            onDownloadStartRequest: onDownloadStart,"}, {"type": "INSERT", "lineNumber": 244, "content": "            onReceivedServerTrustAuthRequest: (controller, challenge) async {"}, {"type": "INSERT", "lineNumber": 245, "content": "              return ServerTrustAuthResponse("}, {"type": "INSERT", "lineNumber": 246, "content": "                  action: ServerTrustAuthResponseAction.PROCEED);"}, {"type": "INSERT", "lineNumber": 247, "content": "            },"}, {"type": "INSERT", "lineNumber": 248, "content": "            initialUrlRequest: URLRequest("}, {"type": "INSERT", "lineNumber": 249, "content": "              url: WebUri.uri("}, {"type": "INSERT", "lineNumber": 250, "content": "                Uri.parse("}, {"type": "INSERT", "lineNumber": 251, "content": "                    \"${AppConstants.appUrl}?v=${DateTime.now().millisecondsSinceEpoch}\"),"}, {"type": "INSERT", "lineNumber": 252, "content": "              ),"}]}, {"timestamp": 1758110358077, "changes": [{"type": "MODIFY", "lineNumber": 80, "content": "          return [pickedFile.path];", "oldContent": "          return [pickedFile.path];"}, {"type": "DELETE", "lineNumber": 82, "oldContent": "  Widget build(BuildContext context) {"}, {"type": "DELETE", "lineNumber": 84, "oldContent": "    void onDownloadStart(controller, request) async {"}, {"type": "DELETE", "lineNumber": 87, "oldContent": "      Log.i(\"onDownloadStartSSSS $request\");"}, {"type": "DELETE", "lineNumber": 89, "oldContent": ""}, {"type": "DELETE", "lineNumber": 92, "oldContent": "      final data = await controller.evaluateJavascript("}, {"type": "INSERT", "lineNumber": 90, "content": "  Widget build(BuildContext context) {"}, {"type": "INSERT", "lineNumber": 91, "content": "    void onDownloadStart(controller, request) async {"}, {"type": "INSERT", "lineNumber": 92, "content": "      Log.i(\"onDownloadStartSSSS $request\");"}, {"type": "INSERT", "lineNumber": 93, "content": ""}, {"type": "INSERT", "lineNumber": 94, "content": "      final data = await controller.evaluateJavascript("}, {"type": "MODIFY", "lineNumber": 138, "content": "              appConfig.onWebViewCreated(controller);", "oldContent": "              await appConfig.onWebViewCreated(controller);"}, {"type": "DELETE", "lineNumber": 146, "oldContent": "                mediaPlaybackRequiresUserGesture: false,"}, {"type": "INSERT", "lineNumber": 148, "content": "                mediaPlaybackRequiresUserGesture: false,"}, {"type": "INSERT", "lineNumber": 157, "content": "                    final filePaths = await _handleFileUpload();"}, {"type": "INSERT", "lineNumber": 158, "content": "                    if (filePaths.isNotEmpty) {"}, {"type": "INSERT", "lineNumber": 159, "content": "                      return {'success': true, 'filePath': filePaths.first};"}, {"type": "INSERT", "lineNumber": 160, "content": "                    }"}, {"type": "INSERT", "lineNumber": 161, "content": "                    return {'success': false, 'error': 'No file selected'};"}, {"type": "INSERT", "lineNumber": 162, "content": "                  } catch (e) {"}, {"type": "INSERT", "lineNumber": 163, "content": "                    Log.e('Error in file upload handler: $e');"}, {"type": "INSERT", "lineNumber": 164, "content": "                    return {'success': false, 'error': e.toString()};"}, {"type": "INSERT", "lineNumber": 165, "content": "                  }"}, {"type": "INSERT", "lineNumber": 166, "content": "                },"}, {"type": "INSERT", "lineNumber": 167, "content": "              );"}, {"type": "INSERT", "lineNumber": 168, "content": ""}, {"type": "INSERT", "lineNumber": 169, "content": "              // Force reload to get latest content"}, {"type": "INSERT", "lineNumber": 170, "content": "              await _forceReload();"}, {"type": "INSERT", "lineNumber": 171, "content": "            },"}, {"type": "DELETE", "lineNumber": 160, "oldContent": "                    final filePaths = await _handleFileUpload();"}, {"type": "DELETE", "lineNumber": 163, "oldContent": "                    if (filePaths.isNotEmpty) {"}, {"type": "DELETE", "lineNumber": 167, "oldContent": "                      return {'success': true, 'filePath': filePaths.first};"}, {"type": "DELETE", "lineNumber": 170, "oldContent": "                    }"}, {"type": "DELETE", "lineNumber": 172, "oldContent": "                    return {'success': false, 'error': 'No file selected'};"}, {"type": "DELETE", "lineNumber": 174, "oldContent": "                  } catch (e) {"}, {"type": "DELETE", "lineNumber": 176, "oldContent": "                    Log.e('Error in file upload handler: $e');"}, {"type": "DELETE", "lineNumber": 178, "oldContent": "                    return {'success': false, 'error': e.toString()};"}, {"type": "DELETE", "lineNumber": 180, "oldContent": "                  }"}, {"type": "DELETE", "lineNumber": 182, "oldContent": "                },"}, {"type": "DELETE", "lineNumber": 184, "oldContent": "              );"}, {"type": "DELETE", "lineNumber": 186, "oldContent": ""}, {"type": "DELETE", "lineNumber": 188, "oldContent": "              // Force reload to get latest content"}, {"type": "DELETE", "lineNumber": 190, "oldContent": "              await _forceReload();"}, {"type": "DELETE", "lineNumber": 192, "oldContent": "            },"}, {"type": "DELETE", "lineNumber": 194, "oldContent": "            ),"}, {"type": "DELETE", "lineNumber": 196, "oldContent": "          );"}, {"type": "DELETE", "lineNumber": 198, "oldContent": "        },"}, {"type": "DELETE", "lineNumber": 200, "oldContent": "      ),"}, {"type": "DELETE", "lineNumber": 202, "oldContent": "    ));"}, {"type": "DELETE", "lineNumber": 204, "oldContent": "  }"}, {"type": "DELETE", "lineNumber": 206, "oldContent": "}"}, {"type": "DELETE", "lineNumber": 208, "oldContent": ""}, {"type": "DELETE", "lineNumber": 210, "oldContent": "              ),"}, {"type": "DELETE", "lineNumber": 212, "oldContent": "                    \"${AppConstants.appUrl}?v=${DateTime.now().millisecondsSinceEpoch}\"),"}, {"type": "DELETE", "lineNumber": 214, "oldContent": "                Uri.parse("}, {"type": "DELETE", "lineNumber": 216, "oldContent": "              url: WebUri.uri("}, {"type": "DELETE", "lineNumber": 218, "oldContent": "            initialUrlRequest: URLRequest("}, {"type": "DELETE", "lineNumber": 220, "oldContent": "            },"}, {"type": "DELETE", "lineNumber": 222, "oldContent": "                  action: ServerTrustAuthResponseAction.PROCEED);"}, {"type": "DELETE", "lineNumber": 224, "oldContent": "              return ServerTrustAuthResponse("}, {"type": "DELETE", "lineNumber": 226, "oldContent": "            onReceivedServerTrustAuthRequest: (controller, challenge) async {"}, {"type": "DELETE", "lineNumber": 228, "oldContent": "            onDownloadStartRequest: onDownloadStart,"}, {"type": "DELETE", "lineNumber": 230, "oldContent": "            },"}, {"type": "DELETE", "lineNumber": 232, "oldContent": "              await appConfig.addTokenToLogin(controller: controller);"}, {"type": "DELETE", "lineNumber": 234, "oldContent": "            onUpdateVisitedHistory: (controller, url, androidIsReload) async {"}, {"type": "DELETE", "lineNumber": 236, "oldContent": "            },"}, {"type": "DELETE", "lineNumber": 238, "oldContent": "              }"}, {"type": "DELETE", "lineNumber": 240, "oldContent": "                await appConfig.addTokenToLogin(controller: controller);"}, {"type": "DELETE", "lineNumber": 242, "oldContent": "              if (progress == 100) {"}, {"type": "DELETE", "lineNumber": 244, "oldContent": "            onProgressChanged: (controller, progress) async {"}, {"type": "DELETE", "lineNumber": 246, "oldContent": "            },"}, {"type": "DELETE", "lineNumber": 248, "oldContent": "              ''');"}, {"type": "DELETE", "lineNumber": 250, "oldContent": "                })();"}, {"type": "DELETE", "lineNumber": 252, "oldContent": "                  });"}, {"type": "DELETE", "lineNumber": 254, "oldContent": "                    subtree: true"}, {"type": "DELETE", "lineNumber": 256, "oldContent": "                    childList: true,"}, {"type": "INSERT", "lineNumber": 226, "content": "                  });"}, {"type": "INSERT", "lineNumber": 227, "content": ""}, {"type": "INSERT", "lineNumber": 229, "content": "                    childList: true,"}, {"type": "INSERT", "lineNumber": 230, "content": "                    subtree: true"}, {"type": "INSERT", "lineNumber": 232, "content": "                })();"}, {"type": "INSERT", "lineNumber": 233, "content": "              ''');"}, {"type": "INSERT", "lineNumber": 234, "content": "            },"}, {"type": "INSERT", "lineNumber": 235, "content": "            onProgressChanged: (controller, progress) async {"}, {"type": "INSERT", "lineNumber": 236, "content": "              if (progress == 100) {"}, {"type": "INSERT", "lineNumber": 237, "content": "                await appConfig.addTokenToLogin(controller: controller);"}, {"type": "INSERT", "lineNumber": 238, "content": "              }"}, {"type": "INSERT", "lineNumber": 239, "content": "            },"}, {"type": "INSERT", "lineNumber": 240, "content": "            onUpdateVisitedHistory: (controller, url, androidIsReload) async {"}, {"type": "INSERT", "lineNumber": 241, "content": "              await appConfig.addTokenToLogin(controller: controller);"}, {"type": "INSERT", "lineNumber": 242, "content": "            },"}, {"type": "INSERT", "lineNumber": 243, "content": "            onDownloadStartRequest: onDownloadStart,"}, {"type": "INSERT", "lineNumber": 244, "content": "            onReceivedServerTrustAuthRequest: (controller, challenge) async {"}, {"type": "INSERT", "lineNumber": 245, "content": "              return ServerTrustAuthResponse("}, {"type": "INSERT", "lineNumber": 246, "content": "                  action: ServerTrustAuthResponseAction.PROCEED);"}, {"type": "INSERT", "lineNumber": 247, "content": "            },"}, {"type": "INSERT", "lineNumber": 248, "content": "            initialUrlRequest: URLRequest("}, {"type": "INSERT", "lineNumber": 249, "content": "              url: WebUri.uri("}, {"type": "INSERT", "lineNumber": 250, "content": "                Uri.parse("}, {"type": "INSERT", "lineNumber": 251, "content": "                    \"${AppConstants.appUrl}?v=${DateTime.now().millisecondsSinceEpoch}\"),"}, {"type": "INSERT", "lineNumber": 252, "content": "              ),"}, {"type": "INSERT", "lineNumber": 253, "content": "            ),"}, {"type": "INSERT", "lineNumber": 254, "content": "          );"}, {"type": "INSERT", "lineNumber": 255, "content": "        },"}, {"type": "INSERT", "lineNumber": 256, "content": "      ),"}, {"type": "INSERT", "lineNumber": 257, "content": "    ));"}, {"type": "INSERT", "lineNumber": 258, "content": "  }"}, {"type": "INSERT", "lineNumber": 259, "content": "}"}]}, {"timestamp": 1758110366508, "changes": [{"type": "DELETE", "lineNumber": 0, "oldContent": "import 'dart:io';"}, {"type": "INSERT", "lineNumber": 78, "content": "          Log.i('File picked: ${pickedFile.path}');"}, {"type": "DELETE", "lineNumber": 80, "oldContent": "          return [pickedFile.path];"}, {"type": "DELETE", "lineNumber": 86, "oldContent": "  Widget build(BuildContext context) {"}, {"type": "DELETE", "lineNumber": 88, "oldContent": "    void onDownloadStart(controller, request) async {"}, {"type": "INSERT", "lineNumber": 87, "content": ""}, {"type": "INSERT", "lineNumber": 88, "content": "  @override"}, {"type": "INSERT", "lineNumber": 89, "content": "  Widget build(BuildContext context) {"}, {"type": "INSERT", "lineNumber": 90, "content": "    void onDownloadStart(controller, request) async {"}, {"type": "DELETE", "lineNumber": 92, "oldContent": ""}, {"type": "DELETE", "lineNumber": 94, "oldContent": "  @override"}, {"type": "MODIFY", "lineNumber": 147, "content": "                mediaPlaybackRequiresUserGesture: false,", "oldContent": "                mediaPlaybackRequiresUserGesture: false,"}, {"type": "DELETE", "lineNumber": 158, "oldContent": "            onLoadStop: (controller, url) async {"}, {"type": "DELETE", "lineNumber": 160, "oldContent": "              await appConfig.addTokenToLogin(controller: controller);"}, {"type": "DELETE", "lineNumber": 162, "oldContent": ""}, {"type": "DELETE", "lineNumber": 165, "oldContent": "              // Inject JavaScript to handle file input clicks"}, {"type": "DELETE", "lineNumber": 167, "oldContent": "              await controller.evaluateJavascript(source: '''"}, {"type": "DELETE", "lineNumber": 170, "oldContent": "                (function() {"}, {"type": "DELETE", "lineNumber": 172, "oldContent": "                  // Override file input behavior"}, {"type": "DELETE", "lineNumber": 174, "oldContent": "                  function handleFileInputs() {"}, {"type": "DELETE", "lineNumber": 177, "oldContent": "                    const fileInputs = document.querySelectorAll('input[type=\"file\"]');"}, {"type": "DELETE", "lineNumber": 179, "oldContent": "                    fileInputs.forEach(function(input) {"}, {"type": "INSERT", "lineNumber": 171, "content": "            onLoadStop: (controller, url) async {"}, {"type": "INSERT", "lineNumber": 172, "content": "              await appConfig.addTokenToLogin(controller: controller);"}, {"type": "INSERT", "lineNumber": 173, "content": ""}, {"type": "INSERT", "lineNumber": 174, "content": "              // Inject JavaScript to handle file input clicks"}, {"type": "INSERT", "lineNumber": 175, "content": "              await controller.evaluateJavascript(source: '''"}, {"type": "INSERT", "lineNumber": 176, "content": "                (function() {"}, {"type": "INSERT", "lineNumber": 177, "content": "                  // Override file input behavior"}, {"type": "INSERT", "lineNumber": 178, "content": "                  function handleFileInputs() {"}, {"type": "INSERT", "lineNumber": 179, "content": "                    const fileInputs = document.querySelectorAll('input[type=\"file\"]');"}, {"type": "INSERT", "lineNumber": 180, "content": "                    fileInputs.forEach(function(input) {"}, {"type": "INSERT", "lineNumber": 209, "content": "                          });"}, {"type": "INSERT", "lineNumber": 210, "content": "                        });"}, {"type": "INSERT", "lineNumber": 211, "content": "                      }"}, {"type": "INSERT", "lineNumber": 212, "content": "                    });"}, {"type": "INSERT", "lineNumber": 213, "content": "                  }"}, {"type": "INSERT", "lineNumber": 214, "content": ""}, {"type": "INSERT", "lineNumber": 215, "content": "                  // Handle initial file inputs"}, {"type": "INSERT", "lineNumber": 216, "content": "                  handleFileInputs();"}, {"type": "INSERT", "lineNumber": 217, "content": ""}, {"type": "INSERT", "lineNumber": 218, "content": "                  // Handle dynamically added file inputs"}, {"type": "INSERT", "lineNumber": 219, "content": "                  const observer = new MutationObserver(function(mutations) {"}, {"type": "INSERT", "lineNumber": 220, "content": "                    mutations.forEach(function(mutation) {"}, {"type": "INSERT", "lineNumber": 221, "content": "                      if (mutation.type === 'childList') {"}, {"type": "INSERT", "lineNumber": 222, "content": "                        handleFileInputs();"}, {"type": "INSERT", "lineNumber": 223, "content": "                      }"}, {"type": "INSERT", "lineNumber": 224, "content": "                    });"}, {"type": "DELETE", "lineNumber": 212, "oldContent": "                          });"}, {"type": "INSERT", "lineNumber": 227, "content": "                  observer.observe(document.body, {"}, {"type": "DELETE", "lineNumber": 214, "oldContent": "                        });"}, {"type": "DELETE", "lineNumber": 216, "oldContent": "                      }"}, {"type": "INSERT", "lineNumber": 230, "content": "                  });"}, {"type": "DELETE", "lineNumber": 219, "oldContent": "                    });"}, {"type": "DELETE", "lineNumber": 222, "oldContent": "                  }"}, {"type": "DELETE", "lineNumber": 225, "oldContent": ""}, {"type": "DELETE", "lineNumber": 228, "oldContent": "                  // Handle initial file inputs"}, {"type": "DELETE", "lineNumber": 231, "oldContent": "                  handleFileInputs();"}, {"type": "DELETE", "lineNumber": 234, "oldContent": ""}, {"type": "DELETE", "lineNumber": 237, "oldContent": "                  // Handle dynamically added file inputs"}, {"type": "DELETE", "lineNumber": 240, "oldContent": "                  const observer = new MutationObserver(function(mutations) {"}, {"type": "DELETE", "lineNumber": 243, "oldContent": "                    mutations.forEach(function(mutation) {"}, {"type": "DELETE", "lineNumber": 246, "oldContent": "                      if (mutation.type === 'childList') {"}, {"type": "DELETE", "lineNumber": 249, "oldContent": "                        handleFileInputs();"}, {"type": "DELETE", "lineNumber": 252, "oldContent": "                      }"}, {"type": "DELETE", "lineNumber": 255, "oldContent": "                    });"}, {"type": "DELETE", "lineNumber": 257, "oldContent": "                  observer.observe(document.body, {"}, {"type": "DELETE", "lineNumber": 259, "oldContent": "                  });"}]}, {"timestamp": 1758110395779, "changes": [{"type": "MODIFY", "lineNumber": 78, "content": "          Log.i('File picked: ${pickedFile.path}');", "oldContent": "          Log.i('File picked: ${pickedFile.path}');"}, {"type": "DELETE", "lineNumber": 85, "oldContent": ""}, {"type": "INSERT", "lineNumber": 86, "content": "  }"}, {"type": "INSERT", "lineNumber": 87, "content": ""}, {"type": "DELETE", "lineNumber": 89, "oldContent": "  }"}, {"type": "MODIFY", "lineNumber": 147, "content": "                mediaPlaybackRequiresUserGesture: false,", "oldContent": "                mediaPlaybackRequiresUserGesture: false,"}, {"type": "MODIFY", "lineNumber": 151, "content": "              controller.addJavaScriptHandler(", "oldContent": "              await controller.addJavaScriptHandler("}, {"type": "DELETE", "lineNumber": 164, "oldContent": "            onLoadStop: (controller, url) async {"}, {"type": "INSERT", "lineNumber": 165, "content": "                },"}, {"type": "INSERT", "lineNumber": 166, "content": "              );"}, {"type": "INSERT", "lineNumber": 167, "content": ""}, {"type": "INSERT", "lineNumber": 168, "content": "              // Force reload to get latest content"}, {"type": "INSERT", "lineNumber": 169, "content": "              await _forceReload();"}, {"type": "INSERT", "lineNumber": 170, "content": "            },"}, {"type": "INSERT", "lineNumber": 171, "content": "            onLoadStop: (controller, url) async {"}, {"type": "DELETE", "lineNumber": 168, "oldContent": "                },"}, {"type": "DELETE", "lineNumber": 171, "oldContent": "              );"}, {"type": "DELETE", "lineNumber": 173, "oldContent": ""}, {"type": "DELETE", "lineNumber": 176, "oldContent": "              // Force reload to get latest content"}, {"type": "DELETE", "lineNumber": 179, "oldContent": "              await _forceReload();"}, {"type": "DELETE", "lineNumber": 180, "oldContent": "            },"}, {"type": "MODIFY", "lineNumber": 209, "content": "                          });", "oldContent": "                          });"}, {"type": "DELETE", "lineNumber": 211, "oldContent": "                  });"}, {"type": "DELETE", "lineNumber": 213, "oldContent": ""}, {"type": "DELETE", "lineNumber": 216, "oldContent": "                    childList: true,"}, {"type": "DELETE", "lineNumber": 219, "oldContent": "                    subtree: true"}, {"type": "DELETE", "lineNumber": 222, "oldContent": "                })();"}, {"type": "DELETE", "lineNumber": 224, "oldContent": "              ''');"}, {"type": "DELETE", "lineNumber": 227, "oldContent": "            },"}, {"type": "DELETE", "lineNumber": 229, "oldContent": "            onProgressChanged: (controller, progress) async {"}, {"type": "DELETE", "lineNumber": 232, "oldContent": "              if (progress == 100) {"}, {"type": "INSERT", "lineNumber": 225, "content": "                  });"}, {"type": "INSERT", "lineNumber": 226, "content": ""}, {"type": "INSERT", "lineNumber": 227, "content": "                  observer.observe(document.body, {"}, {"type": "INSERT", "lineNumber": 228, "content": "                    childList: true,"}, {"type": "INSERT", "lineNumber": 229, "content": "                    subtree: true"}, {"type": "INSERT", "lineNumber": 230, "content": "                  });"}, {"type": "INSERT", "lineNumber": 231, "content": "                })();"}, {"type": "INSERT", "lineNumber": 232, "content": "              ''');"}, {"type": "INSERT", "lineNumber": 233, "content": "            },"}, {"type": "INSERT", "lineNumber": 234, "content": "            onProgressChanged: (controller, progress) async {"}, {"type": "INSERT", "lineNumber": 235, "content": "              if (progress == 100) {"}, {"type": "DELETE", "lineNumber": 236, "oldContent": "                  observer.observe(document.body, {"}, {"type": "DELETE", "lineNumber": 239, "oldContent": "                  });"}]}]}, "/Users/<USER>/Flutter-Projects/Ajory/New School/teacher-mobile-app/lib/src/core/config/app_config.dart": {"filePath": "/Users/<USER>/Flutter-Projects/Ajory/New School/teacher-mobile-app/lib/src/core/config/app_config.dart", "baseContent": "import 'dart:async';\nimport 'dart:developer';\n\nimport 'package:flutter/material.dart';\n// import 'package:webview_flutter/webview_flutter.dart';\nimport 'package:flutter_inappwebview/flutter_inappwebview.dart';\nimport 'package:internet_connection_checker/internet_connection_checker.dart';\nimport 'package:permission_handler/permission_handler.dart';\nimport 'package:restart_app/restart_app.dart';\nimport 'package:up_school_parent/src/core/utils/app_constants.dart';\nimport 'package:up_school_parent/src/core/utils/one_signal_service.dart';\nimport 'package:xr_helper/xr_helper.dart';\n\nclass AppConfig extends ChangeNotifier {\n  //? Loading\n  bool isLoading = true;\n\n  Future<void> requestCameraPermission() async {\n    final status = await Permission.camera.request();\n    if (status == PermissionStatus.granted) {\n      // Permission granted.\n    } else if (status == PermissionStatus.denied) {\n      // Permission denied.\n    } else if (status == PermissionStatus.permanentlyDenied) {\n      // Permission permanently denied.\n    }\n  }\n\n  set loading(bool value) {\n    isLoading = value;\n    notifyListeners();\n  }\n\n  //? Check Internet Connection\n  bool hasInternet = true;\n\n  //? Initialize App\n  Future<void> init() async {\n    try {\n      loading = true;\n\n      // await AppConfig.addFcmTokenToUrl();\n\n      await checkInternetConnection();\n\n      if (!hasInternet) {\n        loading = false;\n        return;\n      }\n\n      log('URL ${AppConstants.appUrl}');\n    } on Exception catch (e) {\n      log('Error $e');\n\n      loading = false;\n    }\n  }\n\n  static Future<void> addFcmTokenToUrl() async {\n    try {\n      final token = OneSignalNotificationService.getUserId();\n\n      AppConstants.appUrl += '?token=$token';\n    } on Exception catch (e) {\n      log('Error $e');\n    }\n  }\n\n  //? Check Internet Connection\n  Future<void> checkInternetConnection() async {\n    hasInternet = await InternetConnectionChecker().hasConnection;\n    notifyListeners();\n  }\n\n  // InAppWebViewController? webViewController;\n\n  late final InAppWebViewController webViewController;\n\n  //? with flutter_inappwebview package\n  Future<void> onWebViewCreated(InAppWebViewController controller) async {\n    try {\n      webViewController = controller..set;\n\n      await addTokenToLogin(controller: controller);\n\n      notifyListeners();\n    } on TimeoutException catch (_) {\n      log('Timeout occurred');\n\n      loading = false;\n\n      Restart.restartApp();\n    } on Exception catch (e) {\n      log('Error $e');\n\n      loading = false;\n\n      Restart.restartApp();\n    }\n  }\n\n  Future<void> addTokenToLogin({\n    required InAppWebViewController controller,\n  }) async {\n    final currentUrl = await controller.getUrl();\n\n    final uri = Uri.parse(currentUrl.toString());\n    final tokenInUrl = uri.queryParameters['token'];\n\n    final shouldAddToken = (tokenInUrl == null || tokenInUrl.trim().isEmpty) &&\n        currentUrl.toString().contains('login');\n\n    if (shouldAddToken) {\n      Log.w('SHOULD ADD TOKEN------');\n      final token = OneSignalNotificationService.getUserId();\n      if (token.isEmpty) {\n        Future.delayed(const Duration(seconds: 5), () async {\n          await addTokenToLogin(controller: controller);\n        });\n      } else {\n        await controller.loadUrl(\n          urlRequest: URLRequest(\n            url: WebUri('${AppConstants.appUrl}?token=$token'),\n            // url: WebUri('${currentUrl.toString()}?token=$token'),\n          ),\n        );\n      }\n    }\n  }\n\n//? Add Token To Login\n// Future<void> addTokenToLogin({\n//   required InAppWebViewController controller,\n// }) async {\n//   final currentUrl = await controller.getUrl();\n//\n//   final shouldAddToken = currentUrl.toString() == AppConstants.appUrl &&\n//       !currentUrl.toString().contains('token=');\n//\n//   if (shouldAddToken) {\n//     Log.w('SHOULD ADD TOKEN------');\n//     final token = OneSignalNotificationService.getUserId();\n//     await controller.loadUrl(\n//       urlRequest: URLRequest(\n//         url: WebUri('${currentUrl.toString()}?token=$token'),\n//       ),\n//     );\n//   }\n// }\n}\n", "baseTimestamp": 1758013119511, "deltas": [{"timestamp": 1758013124588, "changes": [{"type": "MODIFY", "lineNumber": 81, "content": "      webViewController = controller..setSettings(", "oldContent": "      webViewController = controller..set;"}, {"type": "INSERT", "lineNumber": 82, "content": "        settings: InAppWebViewSettings("}, {"type": "INSERT", "lineNumber": 83, "content": "          cacheEnabled: false,"}, {"type": "INSERT", "lineNumber": 84, "content": "          clearCache: true,"}, {"type": "INSERT", "lineNumber": 85, "content": "          domStorageEnabled: true,"}, {"type": "INSERT", "lineNumber": 86, "content": "        ),"}, {"type": "INSERT", "lineNumber": 87, "content": "      );"}]}, {"timestamp": 1758013127255, "changes": [{"type": "DELETE", "lineNumber": 81, "oldContent": "      webViewController = controller..setSettings("}, {"type": "DELETE", "lineNumber": 82, "oldContent": "        settings: InAppWebViewSettings("}, {"type": "INSERT", "lineNumber": 81, "content": "      webViewController = controller;"}, {"type": "DELETE", "lineNumber": 84, "oldContent": "          cacheEnabled: false,"}, {"type": "DELETE", "lineNumber": 86, "oldContent": "          clearCache: true,"}, {"type": "DELETE", "lineNumber": 88, "oldContent": "          domStorageEnabled: true,"}, {"type": "DELETE", "lineNumber": 90, "oldContent": "        ),"}, {"type": "DELETE", "lineNumber": 92, "oldContent": "      );"}]}, {"timestamp": 1758013198177, "changes": [{"type": "MODIFY", "lineNumber": 76, "content": "  InAppWebViewController? webViewController;", "oldContent": "  late final InAppWebViewController webViewController;"}, {"type": "INSERT", "lineNumber": 81, "content": "      //? set loading"}, {"type": "INSERT", "lineNumber": 82, "content": "      // loading = false;"}, {"type": "INSERT", "lineNumber": 83, "content": ""}, {"type": "INSERT", "lineNumber": 84, "content": "      //? set controller"}]}, {"timestamp": 1758014887763, "changes": [{"type": "DELETE", "lineNumber": 82, "oldContent": "      webViewController = controller;"}, {"type": "INSERT", "lineNumber": 84, "content": "      //? set controller"}, {"type": "INSERT", "lineNumber": 85, "content": "      webViewController = controller;"}, {"type": "DELETE", "lineNumber": 87, "oldContent": "      //? set controller"}, {"type": "INSERT", "lineNumber": 127, "content": "            headers: {"}, {"type": "INSERT", "lineNumber": 128, "content": "              'Cache-Control': 'no-cache, no-store, must-revalidate',"}, {"type": "INSERT", "lineNumber": 129, "content": "              'Pragma': 'no-cache',"}, {"type": "INSERT", "lineNumber": 130, "content": "              'Expires': '0',"}, {"type": "INSERT", "lineNumber": 131, "content": "            },"}]}]}, "/terminal_output": {"filePath": "/terminal_output", "baseContent": "xrgouda@Amrs-MacBook-Air teacher-mobile-app % \n\n\n\n\n\n\n\n\n\n\n\n\n\n", "baseTimestamp": 1758013161855, "deltas": [{"timestamp": 1758013219678, "changes": [{"type": "MODIFY", "lineNumber": 0, "content": "xrgouda@Amrs-MacBook-Air teacher-mobile-app % GIT_SSH_COMMAND=\"ssh -i ~/.ssh/id_rsa_ajory\" git push origin main", "oldContent": "xrgouda@Amrs-MacBook-Air teacher-mobile-app % "}]}, {"timestamp": 1758013224262, "changes": [{"type": "INSERT", "lineNumber": 1, "content": "Enumerating objects: 19, done."}, {"type": "INSERT", "lineNumber": 2, "content": "Counting objects: 100% (19/19), done."}, {"type": "INSERT", "lineNumber": 3, "content": "Delta compression using up to 8 threads"}, {"type": "INSERT", "lineNumber": 4, "content": "Compressing objects: 100% (10/10), done."}, {"type": "INSERT", "lineNumber": 5, "content": "Writing objects: 100% (11/11), 5.00 KiB | 1024.00 KiB/s, done."}, {"type": "INSERT", "lineNumber": 6, "content": "Total 11 (delta 3), reused 0 (delta 0), pack-reused 0"}, {"type": "INSERT", "lineNumber": 7, "content": "To gitlab.com:yahyaameen/teacher-mobile-app.git"}, {"type": "INSERT", "lineNumber": 8, "content": "   7d723e4..096f37d  main -> main"}, {"type": "INSERT", "lineNumber": 9, "content": "xrgouda@Amrs-MacBook-Air teacher-mobile-app % "}, {"type": "DELETE", "lineNumber": 6, "oldContent": ""}, {"type": "DELETE", "lineNumber": 7, "oldContent": ""}, {"type": "DELETE", "lineNumber": 8, "oldContent": ""}, {"type": "DELETE", "lineNumber": 9, "oldContent": ""}, {"type": "DELETE", "lineNumber": 10, "oldContent": ""}, {"type": "DELETE", "lineNumber": 11, "oldContent": ""}, {"type": "DELETE", "lineNumber": 12, "oldContent": ""}, {"type": "DELETE", "lineNumber": 13, "oldContent": ""}, {"type": "DELETE", "lineNumber": 14, "oldContent": ""}]}, {"timestamp": 1758014883656, "changes": [{"type": "DELETE", "lineNumber": 0, "oldContent": "xrgouda@Amrs-MacBook-Air teacher-mobile-app % GIT_SSH_COMMAND=\"ssh -i ~/.ssh/id_rsa_ajory\" git push origin main"}, {"type": "DELETE", "lineNumber": 1, "oldContent": "Enumerating objects: 19, done."}, {"type": "INSERT", "lineNumber": 0, "content": "xrgouda@Amrs-MacBook-Air teacher-mobile-app % "}, {"type": "DELETE", "lineNumber": 3, "oldContent": "Counting objects: 100% (19/19), done."}, {"type": "DELETE", "lineNumber": 5, "oldContent": "Delta compression using up to 8 threads"}, {"type": "DELETE", "lineNumber": 7, "oldContent": "Compressing objects: 100% (10/10), done."}, {"type": "DELETE", "lineNumber": 9, "oldContent": "Writing objects: 100% (11/11), 5.00 KiB | 1024.00 KiB/s, done."}, {"type": "DELETE", "lineNumber": 11, "oldContent": "Total 11 (delta 3), reused 0 (delta 0), pack-reused 0"}, {"type": "DELETE", "lineNumber": 12, "oldContent": "To gitlab.com:yahyaameen/teacher-mobile-app.git"}, {"type": "DELETE", "lineNumber": 13, "oldContent": "   7d723e4..096f37d  main -> main"}, {"type": "DELETE", "lineNumber": 14, "oldContent": "xrgouda@Amrs-MacBook-Air teacher-mobile-app % "}, {"type": "INSERT", "lineNumber": 6, "content": ""}, {"type": "INSERT", "lineNumber": 7, "content": ""}, {"type": "INSERT", "lineNumber": 8, "content": ""}, {"type": "INSERT", "lineNumber": 9, "content": ""}, {"type": "INSERT", "lineNumber": 10, "content": ""}, {"type": "INSERT", "lineNumber": 11, "content": ""}, {"type": "INSERT", "lineNumber": 12, "content": ""}, {"type": "INSERT", "lineNumber": 13, "content": ""}, {"type": "INSERT", "lineNumber": 14, "content": ""}]}, {"timestamp": 1758014898356, "changes": [{"type": "MODIFY", "lineNumber": 0, "content": "xrgouda@Amrs-MacBook-Air teacher-mobile-app % GIT_SSH_COMMAND=\"ssh -i ~/.ssh/id_rsa_ajory\" git push origin main", "oldContent": "xrgouda@Amrs-MacBook-Air teacher-mobile-app % "}]}, {"timestamp": 1758098906280, "changes": [{"type": "MODIFY", "lineNumber": 0, "content": "xrgouda@Amrs-MacBook-Air teacher-mobile-app % ", "oldContent": "xrgouda@Amrs-MacBook-Air teacher-mobile-app % GIT_SSH_COMMAND=\"ssh -i ~/.ssh/id_rsa_ajory\" git push origin main"}]}]}, "/Dummy.txt": {"filePath": "/Dummy.txt", "baseContent": "Updates", "baseTimestamp": 1758013203426}, "/Users/<USER>/Flutter-Projects/Ajory/New School/teacher-mobile-app/android/app/src/main/AndroidManifest.xml": {"filePath": "/Users/<USER>/Flutter-Projects/Ajory/New School/teacher-mobile-app/android/app/src/main/AndroidManifest.xml", "baseContent": "<manifest xmlns:android=\"http://schemas.android.com/apk/res/android\"\n    xmlns:tools=\"http://schemas.android.com/tools\">\n\n    <uses-permission android:name=\"android.permission.INTERNET\" />\n    <uses-permission android:name=\"android.permission.WRITE_EXTERNAL_STORAGE\" />\n    <uses-permission android:name=\"android.permission.READ_EXTERNAL_STORAGE\" />\n    <uses-permission android:name=\"android.permission.CAMERA\" />\n    <uses-permission android:name=\"android.permission.READ_MEDIA_IMAGES\" tools:node=\"remove\" />\n    <uses-permission android:name=\"android.permission.READ_MEDIA_VIDEO\" tools:node=\"remove\" />\n\n\n    <application\n        android:name=\"${applicationName}\"\n        android:icon=\"@mipmap/ic_launcher\"\n        android:label=\"UPSchool Teacher\"\n        android:requestLegacyExternalStorage=\"true\">\n\n\n\n        <activity\n            android:name=\".MainActivity\"\n            android:configChanges=\"orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode\"\n            android:exported=\"true\"\n            android:hardwareAccelerated=\"true\"\n            android:launchMode=\"singleTop\"\n            android:theme=\"@style/LaunchTheme\"\n            android:usesCleartextTraffic=\"true\"\n            android:windowSoftInputMode=\"adjustResize\">\n            <!-- Specifies an Android theme to apply to this Activity as soon as\n                 the Android process has started. This theme is visible to the user\n                 while the Flutter UI initializes. After that, this theme continues\n                 to determine the Window background behind the Flutter UI. -->\n            <meta-data\n                android:name=\"io.flutter.embedding.android.NormalTheme\"\n                android:resource=\"@style/NormalTheme\" />\n            <intent-filter>\n                <action android:name=\"android.intent.action.MAIN\" />\n                <category android:name=\"android.intent.category.LAUNCHER\" />\n            </intent-filter>\n        </activity>\n        <!-- Don't delete the meta-data below.\n             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java -->\n        <meta-data\n            android:name=\"flutterEmbedding\"\n            android:value=\"2\" />\n    </application>\n</manifest>\n\n", "baseTimestamp": 1758109912249, "deltas": [{"timestamp": 1758110196430, "changes": [{"type": "INSERT", "lineNumber": 17, "content": "        <provider"}, {"type": "INSERT", "lineNumber": 18, "content": "            android:name=\"androidx.core.content.FileProvider\""}, {"type": "INSERT", "lineNumber": 19, "content": "            android:authorities=\"${applicationId}.fileprovider\""}, {"type": "INSERT", "lineNumber": 20, "content": "            android:exported=\"false\""}, {"type": "INSERT", "lineNumber": 21, "content": "            android:grantUriPermissions=\"true\">"}, {"type": "INSERT", "lineNumber": 22, "content": "            <meta-data"}, {"type": "INSERT", "lineNumber": 23, "content": "                android:name=\"android.support.FILE_PROVIDER_PATHS\""}, {"type": "INSERT", "lineNumber": 24, "content": "                android:resource=\"@xml/file_paths\" />"}, {"type": "INSERT", "lineNumber": 25, "content": "        </provider>"}, {"type": "DELETE", "lineNumber": 18, "oldContent": ""}]}]}}}