{"snapshots": {"/Users/<USER>/Flutter-Projects/Ajory/New School/teacher-mobile-app/lib/src/pages/web_view_page.dart": {"filePath": "/Users/<USER>/Flutter-Projects/Ajory/New School/teacher-mobile-app/lib/src/pages/web_view_page.dart", "baseContent": "import 'package:flutter/material.dart';\nimport 'package:flutter_inappwebview/flutter_inappwebview.dart';\nimport 'package:provider/provider.dart';\nimport 'package:up_school_parent/src/core/utils/app_constants.dart';\nimport 'package:up_school_parent/src/pages/services/download_methods.dart';\nimport 'package:up_school_parent/src/pages/widgets/no_internet_connection_widget.dart';\nimport 'package:xr_helper/xr_helper.dart';\n\nimport '../core/config/app_config.dart';\n\nclass WebViewPage extends StatefulWidget {\n  const WebViewPage({super.key});\n\n  @override\n  State<WebViewPage> createState() => _WebViewPageState();\n}\n\nclass _WebViewPageState extends State<WebViewPage> {\n  InAppWebViewController? webViewController;\n\n  @override\n  void initState() {\n    super.initState();\n    // Force reload when the widget is initialized\n    WidgetsBinding.instance.addPostFrameCallback((_) {\n      _forceReload();\n    });\n  }\n\n  // Force reload the WebView to get latest content\n  Future<void> _forceReload() async {\n    if (webViewController != null) {\n      await webViewController!.reload();\n    }\n  }\n  \n  @override\n  Widget build(BuildContext context) {\n    void onDownloadStart(controller, request) async {\n      Log.i(\"onDownloadStartSSSS $request\");\n\n      final data = await controller.evaluateJavascript(\n          source: \"window.localStorage.getItem('UPSTlink')\");\n\n      Log.i(\"onDownloadStartSSSS444 $data\");\n\n      try {\n        await prepareSaveDir();\n        {\n          final lang = await controller.evaluateJavascript(\n              source: \"window.localStorage.getItem('UPSTlang')\");\n\n          final isArabic = lang.toString().contains(\"ar\");\n\n          context.showBarMessage(\n              isArabic ? \"...جار ي الفتح\" : \"פותח את הקובץ...\");\n\n          downloadFiles(context,controller: controller);\n        }\n      } catch (e) {\n        Log.e('DownloadError: $e');\n      }\n    }\n\n    return Scaffold(body: SafeArea(\n      child: Consumer<AppConfig>(\n        builder: (context, appConfig, child) {\n          if (!appConfig.hasInternet) {\n            return const NoInternetConnectionWidget();\n          }\n\n          return InAppWebView(\n            onWebViewCreated: appConfig.onWebViewCreated,\n            onLoadStop: (controller, url) async {\n              await appConfig.addTokenToLogin(controller: controller);\n            },\n            onProgressChanged: (controller, progress) async {\n              if (progress == 100) {\n                await appConfig.addTokenToLogin(controller: controller);\n              }\n            },\n            onUpdateVisitedHistory: (controller, url, androidIsReload) async {\n              await appConfig.addTokenToLogin(controller: controller);\n            },\n            onDownloadStartRequest: onDownloadStart,\n            onReceivedServerTrustAuthRequest: (controller, challenge) async {\n              return ServerTrustAuthResponse(\n                  action: ServerTrustAuthResponseAction.PROCEED);\n            },\n            initialUrlRequest: URLRequest(\n              url: WebUri.uri(\n                Uri.parse(\n                  \"${AppConstants.appUrl}?v=${DateTime.now().millisecondsSinceEpoch}\"\n                ),\n              ),\n            ),\n          );\n        },\n      ),\n    ));\n  }\n}\n", "baseTimestamp": 1758013005658, "deltas": [{"timestamp": 1758013018873, "changes": [{"type": "MODIFY", "lineNumber": 72, "content": "            initialSettings: InAppWebViewSettings(", "oldContent": "            onWebViewCreated: appConfig.onWebViewCreated,"}, {"type": "INSERT", "lineNumber": 73, "content": "              cacheEnabled: false,"}, {"type": "INSERT", "lineNumber": 74, "content": "              clearCache: true,"}, {"type": "INSERT", "lineNumber": 75, "content": "              domStorageEnabled:"}, {"type": "INSERT", "lineNumber": 76, "content": "              true, // Ensure localStorage/sessionStorage work"}, {"type": "INSERT", "lineNumber": 77, "content": "            ),"}, {"type": "INSERT", "lineNumber": 78, "content": "            onWebViewCreated: (controller) async {"}, {"type": "INSERT", "lineNumber": 79, "content": "              webViewController = controller;"}, {"type": "INSERT", "lineNumber": 80, "content": "              await appConfig.onWebViewCreated(controller);"}, {"type": "INSERT", "lineNumber": 81, "content": ""}, {"type": "INSERT", "lineNumber": 82, "content": "              // Configure cache settings to disable caching but preserve sessions"}, {"type": "INSERT", "lineNumber": 83, "content": "              await controller.setSettings("}, {"type": "INSERT", "lineNumber": 84, "content": "                  settings: InAppWebViewSettings("}, {"type": "INSERT", "lineNumber": 85, "content": "                    cacheEnabled: false,"}, {"type": "INSERT", "lineNumber": 86, "content": "                    clearCache: true,"}, {"type": "INSERT", "lineNumber": 87, "content": "                    domStorageEnabled: true,"}, {"type": "INSERT", "lineNumber": 88, "content": "                  ));"}, {"type": "INSERT", "lineNumber": 89, "content": ""}, {"type": "INSERT", "lineNumber": 90, "content": "              // Force reload to get latest content"}, {"type": "INSERT", "lineNumber": 91, "content": "              await _forceReload();"}, {"type": "INSERT", "lineNumber": 92, "content": "            },"}]}, {"timestamp": 1758013061354, "changes": [{"type": "DELETE", "lineNumber": 74, "oldContent": "            onLoadStop: (controller, url) async {"}, {"type": "DELETE", "lineNumber": 76, "oldContent": "              await appConfig.addTokenToLogin(controller: controller);"}, {"type": "DELETE", "lineNumber": 78, "oldContent": "            },"}, {"type": "DELETE", "lineNumber": 80, "oldContent": "            onProgressChanged: (controller, progress) async {"}, {"type": "DELETE", "lineNumber": 82, "oldContent": "              if (progress == 100) {"}, {"type": "DELETE", "lineNumber": 84, "oldContent": "                await appConfig.addTokenToLogin(controller: controller);"}, {"type": "DELETE", "lineNumber": 86, "oldContent": "              }"}, {"type": "DELETE", "lineNumber": 88, "oldContent": "            },"}, {"type": "DELETE", "lineNumber": 90, "oldContent": "            onUpdateVisitedHistory: (controller, url, androidIsReload) async {"}, {"type": "DELETE", "lineNumber": 92, "oldContent": "              await appConfig.addTokenToLogin(controller: controller);"}, {"type": "DELETE", "lineNumber": 94, "oldContent": "            },"}, {"type": "DELETE", "lineNumber": 96, "oldContent": "            onDownloadStartRequest: onDownloadStart,"}, {"type": "DELETE", "lineNumber": 98, "oldContent": "            onReceivedServerTrustAuthRequest: (controller, challenge) async {"}, {"type": "DELETE", "lineNumber": 100, "oldContent": "              return ServerTrustAuthResponse("}, {"type": "DELETE", "lineNumber": 102, "oldContent": "                  action: ServerTrustAuthResponseAction.PROCEED);"}, {"type": "DELETE", "lineNumber": 104, "oldContent": "            },"}, {"type": "DELETE", "lineNumber": 106, "oldContent": "            initialUrlRequest: URLRequest("}, {"type": "DELETE", "lineNumber": 108, "oldContent": "              url: WebUri.uri("}, {"type": "DELETE", "lineNumber": 110, "oldContent": "                Uri.parse("}, {"type": "INSERT", "lineNumber": 93, "content": "            onLoadStop: (controller, url) async {"}, {"type": "INSERT", "lineNumber": 94, "content": "              await appConfig.addTokenToLogin(controller: controller);"}, {"type": "INSERT", "lineNumber": 95, "content": "            },"}, {"type": "INSERT", "lineNumber": 96, "content": "            onProgressChanged: (controller, progress) async {"}, {"type": "INSERT", "lineNumber": 97, "content": "              if (progress == 100) {"}, {"type": "INSERT", "lineNumber": 98, "content": "                await appConfig.addTokenToLogin(controller: controller);"}, {"type": "INSERT", "lineNumber": 99, "content": "              }"}, {"type": "INSERT", "lineNumber": 100, "content": "            },"}, {"type": "INSERT", "lineNumber": 101, "content": "            onUpdateVisitedHistory: (controller, url, androidIsReload) async {"}, {"type": "INSERT", "lineNumber": 102, "content": "              await appConfig.addTokenToLogin(controller: controller);"}, {"type": "INSERT", "lineNumber": 103, "content": "            },"}, {"type": "INSERT", "lineNumber": 104, "content": "            onDownloadStartRequest: onDownloadStart,"}, {"type": "INSERT", "lineNumber": 105, "content": "            onReceivedServerTrustAuthRequest: (controller, challenge) async {"}, {"type": "INSERT", "lineNumber": 106, "content": "              return ServerTrustAuthResponse("}, {"type": "INSERT", "lineNumber": 107, "content": "                  action: ServerTrustAuthResponseAction.PROCEED);"}, {"type": "INSERT", "lineNumber": 108, "content": "            },"}, {"type": "INSERT", "lineNumber": 109, "content": "            "}, {"type": "INSERT", "lineNumber": 110, "content": "            initialUrlRequest: URLRequest("}, {"type": "INSERT", "lineNumber": 111, "content": "              url: WebUri.uri("}, {"type": "INSERT", "lineNumber": 112, "content": "                Uri.parse("}]}, {"timestamp": 1758013064848, "changes": [{"type": "MODIFY", "lineNumber": 35, "content": "", "oldContent": "  "}, {"type": "DELETE", "lineNumber": 54, "oldContent": "          context.showBarMessage("}, {"type": "DELETE", "lineNumber": 55, "oldContent": "              isArabic ? \"...جار ي الفتح\" : \"פותח את הקובץ...\");"}, {"type": "INSERT", "lineNumber": 54, "content": "          context"}, {"type": "INSERT", "lineNumber": 55, "content": "              .showBarMessage(isArabic ? \"...جار ي الفتح\" : \"פותח את הקובץ...\");"}, {"type": "MODIFY", "lineNumber": 57, "content": "          downloadFiles(context, controller: controller);", "oldContent": "          downloadFiles(context,controller: controller);"}, {"type": "MODIFY", "lineNumber": 76, "content": "                  true, // Ensure localStorage/sessionStorage work", "oldContent": "              true, // Ensure localStorage/sessionStorage work"}, {"type": "DELETE", "lineNumber": 83, "oldContent": "            onLoadStop: (controller, url) async {"}, {"type": "INSERT", "lineNumber": 84, "content": "                  settings: InAppWebViewSettings("}, {"type": "INSERT", "lineNumber": 85, "content": "                cacheEnabled: false,"}, {"type": "INSERT", "lineNumber": 86, "content": "                clearCache: true,"}, {"type": "INSERT", "lineNumber": 87, "content": "                domStorageEnabled: true,"}, {"type": "INSERT", "lineNumber": 88, "content": "              ));"}, {"type": "INSERT", "lineNumber": 89, "content": ""}, {"type": "INSERT", "lineNumber": 90, "content": "              // Force reload to get latest content"}, {"type": "INSERT", "lineNumber": 91, "content": "              await _forceReload();"}, {"type": "INSERT", "lineNumber": 92, "content": "            },"}, {"type": "INSERT", "lineNumber": 93, "content": "            onLoadStop: (controller, url) async {"}, {"type": "DELETE", "lineNumber": 87, "oldContent": "                  settings: InAppWebViewSettings("}, {"type": "DELETE", "lineNumber": 90, "oldContent": "                    cacheEnabled: false,"}, {"type": "DELETE", "lineNumber": 93, "oldContent": "                    clearCache: true,"}, {"type": "DELETE", "lineNumber": 96, "oldContent": "                    domStorageEnabled: true,"}, {"type": "DELETE", "lineNumber": 99, "oldContent": "                  ));"}, {"type": "DELETE", "lineNumber": 102, "oldContent": ""}, {"type": "DELETE", "lineNumber": 105, "oldContent": "              // Force reload to get latest content"}, {"type": "DELETE", "lineNumber": 107, "oldContent": "            "}, {"type": "DELETE", "lineNumber": 108, "oldContent": "              await _forceReload();"}, {"type": "DELETE", "lineNumber": 111, "oldContent": "            },"}, {"type": "DELETE", "lineNumber": 113, "oldContent": "                  \"${AppConstants.appUrl}?v=${DateTime.now().millisecondsSinceEpoch}\""}, {"type": "DELETE", "lineNumber": 114, "oldContent": "                ),"}, {"type": "INSERT", "lineNumber": 112, "content": "                    \"${AppConstants.appUrl}?v=${DateTime.now().millisecondsSinceEpoch}\"),"}]}, {"timestamp": 1758013104443, "changes": [{"type": "MODIFY", "lineNumber": 84, "content": "                  settings: InAppWebViewSettings(", "oldContent": "                  settings: InAppWebViewSettings("}, {"type": "DELETE", "lineNumber": 86, "oldContent": "              await appConfig.addTokenToLogin(controller: controller);"}, {"type": "DELETE", "lineNumber": 88, "oldContent": "            },"}, {"type": "DELETE", "lineNumber": 91, "oldContent": "            onProgressChanged: (controller, progress) async {"}, {"type": "DELETE", "lineNumber": 93, "oldContent": "              if (progress == 100) {"}, {"type": "DELETE", "lineNumber": 96, "oldContent": "                await appConfig.addTokenToLogin(controller: controller);"}, {"type": "DELETE", "lineNumber": 98, "oldContent": "              }"}, {"type": "INSERT", "lineNumber": 94, "content": "              await appConfig.addTokenToLogin(controller: controller);"}, {"type": "INSERT", "lineNumber": 96, "content": "            onProgressChanged: (controller, progress) async {"}, {"type": "INSERT", "lineNumber": 97, "content": "              if (progress == 100) {"}, {"type": "INSERT", "lineNumber": 98, "content": "                await appConfig.addTokenToLogin(controller: controller);"}, {"type": "INSERT", "lineNumber": 99, "content": "              }"}, {"type": "INSERT", "lineNumber": 100, "content": "            },"}, {"type": "MODIFY", "lineNumber": 112, "content": "                    \"${AppConstants.appUrl}?v=${DateTime.now().millisecondsSinceEpoch}\"),", "oldContent": "                    \"${AppConstants.appUrl}?v=${DateTime.now().millisecondsSinceEpoch}\"),"}]}, {"timestamp": 1758013113429, "changes": [{"type": "INSERT", "lineNumber": 83, "content": "              await controller.setSettings("}, {"type": "DELETE", "lineNumber": 84, "oldContent": "                  settings: InAppWebViewSettings("}, {"type": "DELETE", "lineNumber": 90, "oldContent": "              await appConfig.addTokenToLogin(controller: controller);"}, {"type": "INSERT", "lineNumber": 92, "content": "            },"}, {"type": "INSERT", "lineNumber": 93, "content": "            onLoadStop: (controller, url) async {"}, {"type": "INSERT", "lineNumber": 94, "content": "              await appConfig.addTokenToLogin(controller: controller);"}, {"type": "INSERT", "lineNumber": 95, "content": "            },"}, {"type": "DELETE", "lineNumber": 95, "oldContent": "            },"}, {"type": "DELETE", "lineNumber": 98, "oldContent": "            onLoadStop: (controller, url) async {"}, {"type": "DELETE", "lineNumber": 100, "oldContent": "            },"}, {"type": "INSERT", "lineNumber": 111, "content": "                Uri.parse("}, {"type": "DELETE", "lineNumber": 112, "oldContent": "                    \"${AppConstants.appUrl}?v=${DateTime.now().millisecondsSinceEpoch}\"),"}]}, {"timestamp": 1758013138007, "changes": [{"type": "MODIFY", "lineNumber": 92, "content": "            },", "oldContent": "            },"}, {"type": "DELETE", "lineNumber": 94, "oldContent": "            onProgressChanged: (controller, progress) async {"}, {"type": "MODIFY", "lineNumber": 96, "content": "            onProgressChanged: (controller, progress) async {", "oldContent": "              if (progress == 100) {"}, {"type": "INSERT", "lineNumber": 97, "content": "              if (progress == 100) {"}]}, {"timestamp": 1758013143163, "changes": [{"type": "INSERT", "lineNumber": 91, "content": "              await _forceReload();"}, {"type": "DELETE", "lineNumber": 92, "oldContent": "            },"}, {"type": "INSERT", "lineNumber": 95, "content": "            },"}, {"type": "DELETE", "lineNumber": 97, "oldContent": "            },"}]}]}, "/Users/<USER>/Flutter-Projects/Ajory/New School/teacher-mobile-app/lib/src/core/config/app_config.dart": {"filePath": "/Users/<USER>/Flutter-Projects/Ajory/New School/teacher-mobile-app/lib/src/core/config/app_config.dart", "baseContent": "import 'dart:async';\nimport 'dart:developer';\n\nimport 'package:flutter/material.dart';\n// import 'package:webview_flutter/webview_flutter.dart';\nimport 'package:flutter_inappwebview/flutter_inappwebview.dart';\nimport 'package:internet_connection_checker/internet_connection_checker.dart';\nimport 'package:permission_handler/permission_handler.dart';\nimport 'package:restart_app/restart_app.dart';\nimport 'package:up_school_parent/src/core/utils/app_constants.dart';\nimport 'package:up_school_parent/src/core/utils/one_signal_service.dart';\nimport 'package:xr_helper/xr_helper.dart';\n\nclass AppConfig extends ChangeNotifier {\n  //? Loading\n  bool isLoading = true;\n\n  Future<void> requestCameraPermission() async {\n    final status = await Permission.camera.request();\n    if (status == PermissionStatus.granted) {\n      // Permission granted.\n    } else if (status == PermissionStatus.denied) {\n      // Permission denied.\n    } else if (status == PermissionStatus.permanentlyDenied) {\n      // Permission permanently denied.\n    }\n  }\n\n  set loading(bool value) {\n    isLoading = value;\n    notifyListeners();\n  }\n\n  //? Check Internet Connection\n  bool hasInternet = true;\n\n  //? Initialize App\n  Future<void> init() async {\n    try {\n      loading = true;\n\n      // await AppConfig.addFcmTokenToUrl();\n\n      await checkInternetConnection();\n\n      if (!hasInternet) {\n        loading = false;\n        return;\n      }\n\n      log('URL ${AppConstants.appUrl}');\n    } on Exception catch (e) {\n      log('Error $e');\n\n      loading = false;\n    }\n  }\n\n  static Future<void> addFcmTokenToUrl() async {\n    try {\n      final token = OneSignalNotificationService.getUserId();\n\n      AppConstants.appUrl += '?token=$token';\n    } on Exception catch (e) {\n      log('Error $e');\n    }\n  }\n\n  //? Check Internet Connection\n  Future<void> checkInternetConnection() async {\n    hasInternet = await InternetConnectionChecker().hasConnection;\n    notifyListeners();\n  }\n\n  // InAppWebViewController? webViewController;\n\n  late final InAppWebViewController webViewController;\n\n  //? with flutter_inappwebview package\n  Future<void> onWebViewCreated(InAppWebViewController controller) async {\n    try {\n      webViewController = controller..set;\n\n      await addTokenToLogin(controller: controller);\n\n      notifyListeners();\n    } on TimeoutException catch (_) {\n      log('Timeout occurred');\n\n      loading = false;\n\n      Restart.restartApp();\n    } on Exception catch (e) {\n      log('Error $e');\n\n      loading = false;\n\n      Restart.restartApp();\n    }\n  }\n\n  Future<void> addTokenToLogin({\n    required InAppWebViewController controller,\n  }) async {\n    final currentUrl = await controller.getUrl();\n\n    final uri = Uri.parse(currentUrl.toString());\n    final tokenInUrl = uri.queryParameters['token'];\n\n    final shouldAddToken = (tokenInUrl == null || tokenInUrl.trim().isEmpty) &&\n        currentUrl.toString().contains('login');\n\n    if (shouldAddToken) {\n      Log.w('SHOULD ADD TOKEN------');\n      final token = OneSignalNotificationService.getUserId();\n      if (token.isEmpty) {\n        Future.delayed(const Duration(seconds: 5), () async {\n          await addTokenToLogin(controller: controller);\n        });\n      } else {\n        await controller.loadUrl(\n          urlRequest: URLRequest(\n            url: WebUri('${AppConstants.appUrl}?token=$token'),\n            // url: WebUri('${currentUrl.toString()}?token=$token'),\n          ),\n        );\n      }\n    }\n  }\n\n//? Add Token To Login\n// Future<void> addTokenToLogin({\n//   required InAppWebViewController controller,\n// }) async {\n//   final currentUrl = await controller.getUrl();\n//\n//   final shouldAddToken = currentUrl.toString() == AppConstants.appUrl &&\n//       !currentUrl.toString().contains('token=');\n//\n//   if (shouldAddToken) {\n//     Log.w('SHOULD ADD TOKEN------');\n//     final token = OneSignalNotificationService.getUserId();\n//     await controller.loadUrl(\n//       urlRequest: URLRequest(\n//         url: WebUri('${currentUrl.toString()}?token=$token'),\n//       ),\n//     );\n//   }\n// }\n}\n", "baseTimestamp": 1758013119511, "deltas": [{"timestamp": 1758013124588, "changes": [{"type": "MODIFY", "lineNumber": 81, "content": "      webViewController = controller..setSettings(", "oldContent": "      webViewController = controller..set;"}, {"type": "INSERT", "lineNumber": 82, "content": "        settings: InAppWebViewSettings("}, {"type": "INSERT", "lineNumber": 83, "content": "          cacheEnabled: false,"}, {"type": "INSERT", "lineNumber": 84, "content": "          clearCache: true,"}, {"type": "INSERT", "lineNumber": 85, "content": "          domStorageEnabled: true,"}, {"type": "INSERT", "lineNumber": 86, "content": "        ),"}, {"type": "INSERT", "lineNumber": 87, "content": "      );"}]}, {"timestamp": 1758013127255, "changes": [{"type": "DELETE", "lineNumber": 81, "oldContent": "      webViewController = controller..setSettings("}, {"type": "DELETE", "lineNumber": 82, "oldContent": "        settings: InAppWebViewSettings("}, {"type": "INSERT", "lineNumber": 81, "content": "      webViewController = controller;"}, {"type": "DELETE", "lineNumber": 84, "oldContent": "          cacheEnabled: false,"}, {"type": "DELETE", "lineNumber": 86, "oldContent": "          clearCache: true,"}, {"type": "DELETE", "lineNumber": 88, "oldContent": "          domStorageEnabled: true,"}, {"type": "DELETE", "lineNumber": 90, "oldContent": "        ),"}, {"type": "DELETE", "lineNumber": 92, "oldContent": "      );"}]}, {"timestamp": 1758013198177, "changes": [{"type": "MODIFY", "lineNumber": 76, "content": "  InAppWebViewController? webViewController;", "oldContent": "  late final InAppWebViewController webViewController;"}, {"type": "INSERT", "lineNumber": 81, "content": "      //? set loading"}, {"type": "INSERT", "lineNumber": 82, "content": "      // loading = false;"}, {"type": "INSERT", "lineNumber": 83, "content": ""}, {"type": "INSERT", "lineNumber": 84, "content": "      //? set controller"}]}, {"timestamp": 1758014887763, "changes": [{"type": "DELETE", "lineNumber": 82, "oldContent": "      webViewController = controller;"}, {"type": "INSERT", "lineNumber": 84, "content": "      //? set controller"}, {"type": "INSERT", "lineNumber": 85, "content": "      webViewController = controller;"}, {"type": "DELETE", "lineNumber": 87, "oldContent": "      //? set controller"}, {"type": "INSERT", "lineNumber": 127, "content": "            headers: {"}, {"type": "INSERT", "lineNumber": 128, "content": "              'Cache-Control': 'no-cache, no-store, must-revalidate',"}, {"type": "INSERT", "lineNumber": 129, "content": "              'Pragma': 'no-cache',"}, {"type": "INSERT", "lineNumber": 130, "content": "              'Expires': '0',"}, {"type": "INSERT", "lineNumber": 131, "content": "            },"}]}]}, "/terminal_output": {"filePath": "/terminal_output", "baseContent": "xrgouda@Amrs-MacBook-Air teacher-mobile-app % \n\n\n\n\n\n\n\n\n\n\n\n\n\n", "baseTimestamp": 1758013161855, "deltas": [{"timestamp": 1758013219678, "changes": [{"type": "MODIFY", "lineNumber": 0, "content": "xrgouda@Amrs-MacBook-Air teacher-mobile-app % GIT_SSH_COMMAND=\"ssh -i ~/.ssh/id_rsa_ajory\" git push origin main", "oldContent": "xrgouda@Amrs-MacBook-Air teacher-mobile-app % "}]}, {"timestamp": 1758013224262, "changes": [{"type": "INSERT", "lineNumber": 1, "content": "Enumerating objects: 19, done."}, {"type": "INSERT", "lineNumber": 2, "content": "Counting objects: 100% (19/19), done."}, {"type": "INSERT", "lineNumber": 3, "content": "Delta compression using up to 8 threads"}, {"type": "INSERT", "lineNumber": 4, "content": "Compressing objects: 100% (10/10), done."}, {"type": "INSERT", "lineNumber": 5, "content": "Writing objects: 100% (11/11), 5.00 KiB | 1024.00 KiB/s, done."}, {"type": "INSERT", "lineNumber": 6, "content": "Total 11 (delta 3), reused 0 (delta 0), pack-reused 0"}, {"type": "INSERT", "lineNumber": 7, "content": "To gitlab.com:yahyaameen/teacher-mobile-app.git"}, {"type": "INSERT", "lineNumber": 8, "content": "   7d723e4..096f37d  main -> main"}, {"type": "INSERT", "lineNumber": 9, "content": "xrgouda@Amrs-MacBook-Air teacher-mobile-app % "}, {"type": "DELETE", "lineNumber": 6, "oldContent": ""}, {"type": "DELETE", "lineNumber": 7, "oldContent": ""}, {"type": "DELETE", "lineNumber": 8, "oldContent": ""}, {"type": "DELETE", "lineNumber": 9, "oldContent": ""}, {"type": "DELETE", "lineNumber": 10, "oldContent": ""}, {"type": "DELETE", "lineNumber": 11, "oldContent": ""}, {"type": "DELETE", "lineNumber": 12, "oldContent": ""}, {"type": "DELETE", "lineNumber": 13, "oldContent": ""}, {"type": "DELETE", "lineNumber": 14, "oldContent": ""}]}, {"timestamp": 1758014883656, "changes": [{"type": "DELETE", "lineNumber": 0, "oldContent": "xrgouda@Amrs-MacBook-Air teacher-mobile-app % GIT_SSH_COMMAND=\"ssh -i ~/.ssh/id_rsa_ajory\" git push origin main"}, {"type": "DELETE", "lineNumber": 1, "oldContent": "Enumerating objects: 19, done."}, {"type": "INSERT", "lineNumber": 0, "content": "xrgouda@Amrs-MacBook-Air teacher-mobile-app % "}, {"type": "DELETE", "lineNumber": 3, "oldContent": "Counting objects: 100% (19/19), done."}, {"type": "DELETE", "lineNumber": 5, "oldContent": "Delta compression using up to 8 threads"}, {"type": "DELETE", "lineNumber": 7, "oldContent": "Compressing objects: 100% (10/10), done."}, {"type": "DELETE", "lineNumber": 9, "oldContent": "Writing objects: 100% (11/11), 5.00 KiB | 1024.00 KiB/s, done."}, {"type": "DELETE", "lineNumber": 11, "oldContent": "Total 11 (delta 3), reused 0 (delta 0), pack-reused 0"}, {"type": "DELETE", "lineNumber": 12, "oldContent": "To gitlab.com:yahyaameen/teacher-mobile-app.git"}, {"type": "DELETE", "lineNumber": 13, "oldContent": "   7d723e4..096f37d  main -> main"}, {"type": "DELETE", "lineNumber": 14, "oldContent": "xrgouda@Amrs-MacBook-Air teacher-mobile-app % "}, {"type": "INSERT", "lineNumber": 6, "content": ""}, {"type": "INSERT", "lineNumber": 7, "content": ""}, {"type": "INSERT", "lineNumber": 8, "content": ""}, {"type": "INSERT", "lineNumber": 9, "content": ""}, {"type": "INSERT", "lineNumber": 10, "content": ""}, {"type": "INSERT", "lineNumber": 11, "content": ""}, {"type": "INSERT", "lineNumber": 12, "content": ""}, {"type": "INSERT", "lineNumber": 13, "content": ""}, {"type": "INSERT", "lineNumber": 14, "content": ""}]}, {"timestamp": 1758014898356, "changes": [{"type": "MODIFY", "lineNumber": 0, "content": "xrgouda@Amrs-MacBook-Air teacher-mobile-app % GIT_SSH_COMMAND=\"ssh -i ~/.ssh/id_rsa_ajory\" git push origin main", "oldContent": "xrgouda@Amrs-MacBook-Air teacher-mobile-app % "}]}, {"timestamp": 1758098906280, "changes": [{"type": "MODIFY", "lineNumber": 0, "content": "xrgouda@Amrs-MacBook-Air teacher-mobile-app % ", "oldContent": "xrgouda@Amrs-MacBook-Air teacher-mobile-app % GIT_SSH_COMMAND=\"ssh -i ~/.ssh/id_rsa_ajory\" git push origin main"}]}]}, "/Dummy.txt": {"filePath": "/Dummy.txt", "baseContent": "Updates", "baseTimestamp": 1758013203426}, "/Users/<USER>/Flutter-Projects/Ajory/New School/teacher-mobile-app/android/app/src/main/AndroidManifest.xml": {"filePath": "/Users/<USER>/Flutter-Projects/Ajory/New School/teacher-mobile-app/android/app/src/main/AndroidManifest.xml", "baseContent": "<manifest xmlns:android=\"http://schemas.android.com/apk/res/android\"\n    xmlns:tools=\"http://schemas.android.com/tools\">\n\n    <uses-permission android:name=\"android.permission.INTERNET\" />\n    <uses-permission android:name=\"android.permission.WRITE_EXTERNAL_STORAGE\" />\n    <uses-permission android:name=\"android.permission.READ_EXTERNAL_STORAGE\" />\n    <uses-permission android:name=\"android.permission.CAMERA\" />\n    <uses-permission android:name=\"android.permission.READ_MEDIA_IMAGES\" tools:node=\"remove\" />\n    <uses-permission android:name=\"android.permission.READ_MEDIA_VIDEO\" tools:node=\"remove\" />\n\n\n    <application\n        android:name=\"${applicationName}\"\n        android:icon=\"@mipmap/ic_launcher\"\n        android:label=\"UPSchool Teacher\"\n        android:requestLegacyExternalStorage=\"true\">\n\n\n\n        <activity\n            android:name=\".MainActivity\"\n            android:configChanges=\"orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode\"\n            android:exported=\"true\"\n            android:hardwareAccelerated=\"true\"\n            android:launchMode=\"singleTop\"\n            android:theme=\"@style/LaunchTheme\"\n            android:usesCleartextTraffic=\"true\"\n            android:windowSoftInputMode=\"adjustResize\">\n            <!-- Specifies an Android theme to apply to this Activity as soon as\n                 the Android process has started. This theme is visible to the user\n                 while the Flutter UI initializes. After that, this theme continues\n                 to determine the Window background behind the Flutter UI. -->\n            <meta-data\n                android:name=\"io.flutter.embedding.android.NormalTheme\"\n                android:resource=\"@style/NormalTheme\" />\n            <intent-filter>\n                <action android:name=\"android.intent.action.MAIN\" />\n                <category android:name=\"android.intent.category.LAUNCHER\" />\n            </intent-filter>\n        </activity>\n        <!-- Don't delete the meta-data below.\n             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java -->\n        <meta-data\n            android:name=\"flutterEmbedding\"\n            android:value=\"2\" />\n    </application>\n</manifest>\n\n", "baseTimestamp": 1758109912249}}}